<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = '连接账户';
$lang['connect_whatsapp_business'] = '连接Whatsapp商业账户';
$lang['campaigning'] = '活动管理';
$lang['business_account_id_description'] = '您的WhatsApp商业账户（WABA）ID';
$lang['access_token_description'] = '在Facebook开发者门户注册后，您的用户访问令牌';
$lang['whatsapp_business_account_id'] = 'Whatsapp商业账户ID';
$lang['whatsapp_access_token'] = 'Whatsapp访问令牌';
$lang['webhook_callback_url'] = 'Webhook回调URL';
$lang['verify_token'] = '验证令牌';
$lang['connect'] = '连接';
$lang['whatsapp'] = 'Whatsapp';
$lang['one_click_account_connection'] = '一键连接账户';
$lang['connect_your_whatsapp_account'] = '连接您的Whatsapp账户';
$lang['copy'] = '复制';
$lang['copied'] = '已复制！！';
$lang['disconnect'] = '断开连接';
$lang['number'] = '号码';
$lang['number_id'] = '号码ID';
$lang['quality'] = '质量';
$lang['status'] = '状态';
$lang['business_account_id'] = '商业账户ID';
$lang['permissions'] = '权限';
$lang['phone_number_id_description'] = '连接到WhatsApp商业API的电话号码ID。如果您不确定，可以使用GET电话号码ID请求从WhatsApp API检索它（https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers）';
$lang['phone_number_id'] = 'WhatsApp注册电话的号码ID';
$lang['update_details'] = '更新详细信息';

$lang['bots'] = '机器人';
$lang['bots_management'] = '机器人管理';
$lang['create_template_base_bot'] = '创建模板基础机器人';
$lang['create_message_bot'] = '创建消息机器人';
$lang['type'] = '类型';
$lang['message_bot'] = '消息机器人';
$lang['new_template_bot'] = '新模板机器人';
$lang['new_message_bot'] = '新消息机器人';
$lang['bot_name'] = '机器人名称';
$lang['reply_text'] = '回复文本 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="将发送给潜在客户或联系人。您还可以使用{companyname}、{crm_url}或任何其他自定义合并字段，或使用\'@\'符号查找可用的合并字段" data-placement="bottom"></i> <span class="text-muted">(允许的最大字符数为1024)</span>';
$lang['reply_type'] = '回复类型';
$lang['trigger'] = '触发器';
$lang['header'] = '头部';
$lang['footer_bot'] = '底部 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="允许的最大字符数为60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = '选项1：带有回复按钮的机器人';
$lang['bot_with_button_link'] = '选项2：带有按钮链接的机器人 - CTA URL';
$lang['button1'] = '按钮1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="允许的最大字符数为20" data-placement="bottom"></i>';
$lang['button1_id'] = '按钮1 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="允许的最大字符数为256" data-placement="bottom"></i>';
$lang['button2'] = '按钮2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="允许的最大字符数为20" data-placement="bottom"></i>';
$lang['button2_id'] = '按钮2 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="允许的最大字符数为256" data-placement="bottom"></i>';
$lang['button3'] = '按钮3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="允许的最大字符数为20" data-placement="bottom"></i>';
$lang['button3_id'] = '按钮3 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="允许的最大字符数为256" data-placement="bottom"></i>';
$lang['button_name'] = '按钮名称 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="允许的最大字符数为20" data-placement="bottom"></i>';
$lang['button_link'] = '按钮链接';
$lang['enter_name'] = '输入名称';
$lang['select_reply_type'] = '选择回复类型';
$lang['enter_bot_reply_trigger'] = '输入机器人回复触发器';
$lang['enter_header'] = '输入头部';
$lang['enter_footer'] = '输入底部';
$lang['enter_button1'] = '输入按钮1';
$lang['enter_button1_id'] = '输入按钮1 ID';
$lang['enter_button2'] = '输入按钮2';
$lang['enter_button2_id'] = '输入按钮2 ID';
$lang['enter_button3'] = '输入按钮3';
$lang['enter_button3_id'] = '输入按钮3 ID';
$lang['enter_button_name'] = '输入按钮名称';
$lang['enter_button_url'] = '输入按钮网址';
$lang['on_exact_match'] = '回复机器人：完全匹配时';
$lang['when_message_contains'] = '回复机器人：当消息包含时';
$lang['when_client_send_the_first_message'] = '欢迎回复 - 当潜在客户或客户发送第一条消息时';
$lang['bot_create_successfully'] = '机器人创建成功';
$lang['bot_update_successfully'] = '机器人更新成功';
$lang['bot_deleted_successfully'] = '机器人删除成功';
$lang['templates'] = '模板';
$lang['template_data_loaded'] = '模板成功加载';
$lang['load_templates'] = '加载模板';
$lang['template_management'] = '模板管理';


// campaigns
$lang['campaign'] = '活动';
$lang['campaigns'] = '活动';
$lang['send_new_campaign'] = '发送新活动';
$lang['campaign_name'] = '活动名称';
$lang['template'] = '模板';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="按客户，基于联系人的时区" data-placement="left"></i>定时发送时间';
$lang['scheduled_time_description'] = '按客户，基于联系人的时区';
$lang['ignore_scheduled_time_and_send_now'] = '忽略定时，立即发送';
$lang['template'] = '模板';
$lang['leads'] = '线索';
$lang['delivered_to'] = '发送至';
$lang['read_by'] = '已阅读';
$lang['variables'] = '变量';
$lang['body'] = '内容';
$lang['variable'] = '变量';
$lang['match_with_selected_field'] = '与选定字段匹配';
$lang['preview'] = '预览';
$lang['send_campaign'] = '发送活动';
$lang['send_to'] = '发送给';
$lang['send_campaign'] = '发送活动';
$lang['view_campaign'] = '查看活动';
$lang['campaign_daily_task'] = '活动每日任务';
$lang['back'] = '返回';
$lang['phone'] = '电话';
$lang['message'] = '消息';
$lang['currently_type_not_supported'] = '当前<strong> %s </strong> 模板类型不受支持！';
$lang['of_your'] = '您的 ';
$lang['contacts'] = '联系人';
$lang['select_all_leads'] = '选择所有线索';
$lang['select_all_note_leads'] = '如果您选择此项，所有未来的线索都将包含在此活动中。';
$lang['select_all_note_contacts'] = '如果您选择此项，所有未来的联系人都将包含在此活动中。';

$lang['verified_name'] = '验证名称';
$lang['mark_as_default'] = '标记为默认';
$lang['default_number_updated'] = '默认电话号码ID已成功更新';
$lang['currently_using_this_number'] = '当前使用此号码';
$lang['leads'] = '线索';
$lang['pause_campaign'] = '暂停活动';
$lang['resume_campaign'] = '恢复活动';
$lang['campaign_resumed'] = '活动已恢复';
$lang['campaign_paused'] = '活动已暂停';

//Template
$lang['body_data'] = '内容数据';
$lang['category'] = '类别';

// Template bot
$lang['create_new_template_bot'] = '创建新模板机器人';
$lang['template_bot'] = '模板机器人';
$lang['variables'] = '变量';
$lang['preview'] = '预览';
$lang['template'] = '模板';
$lang['bot_content_1'] = '一旦满足联系人发送的消息中的触发规则，将向联系人发送此消息。';
$lang['save_bot'] = '保存机器人';
$lang['please_select_template'] = '请选择模板';
$lang['use_manually_define_value'] = '使用手动定义的值';
$lang['merge_fields'] = '合并字段';
$lang['template_bot_create_successfully'] = '模板机器人成功创建';
$lang['template_bot_update_successfully'] = '模板机器人成功更新';
$lang['text_bot'] = '文本机器人';
$lang['option_2_bot_with_link'] = '选项2：带链接的按钮机器人 - 行动号召（CTA）URL';
$lang['option_3_file'] = '选项3：带文件的机器人';
// Bot settings
$lang['bot'] = '机器人';
$lang['bot_delay_response'] = '预计响应延迟时发送消息';
$lang['bot_delay_response_placeholder'] = '给我一点时间，我会很快给你答案';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = '关系类型';
$lang['select_all'] = '全选';
$lang['total'] = '总计';
$lang['merge_field_note'] = '使用 \'@\' 符号添加合并字段。';
$lang['send_to_all'] = '发送给所有人 ';
$lang['or'] = '或';

$lang['convert_whatsapp_message_to_lead'] = '自动获取新线索（将新WhatsApp消息转换为线索）';
$lang['leads_status'] = '线索状态';
$lang['leads_assigned'] = '线索已分配';
$lang['whatsapp_auto_lead'] = 'WhatsApp自动线索';
$lang['webhooks_label'] = 'WhatsApp接收的数据将被重新发送至';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = '启用WebHooks重新发送';
$lang['chat'] = '聊天';
$lang['black_listed_phone_numbers'] = '黑名单电话';
$lang['sent_status'] = '发送状态';

$lang['active'] = '活动';
$lang['approved'] = '已批准';
$lang['this_month'] = '本月';
$lang['open_chats'] = '开放聊天';
$lang['resolved_conversations'] = '已解决的对话';
$lang['messages_sent'] = '已发送消息';
$lang['account_connected'] = '账户已连接';
$lang['account_disconnected'] = '账户已断开';
$lang['webhook_verify_token'] = 'Webhook验证令牌';
// Chat integration
$lang['chat_message_note'] = '消息将很快发送。请注意，如果是新联系人，直到该联系人开始与您互动，它将不会出现在此列表中！';

$lang['activity_log'] = '活动日志';
$lang['whatsapp_logs'] = 'WhatsApp日志';
$lang['response_code'] = '响应代码';
$lang['recorded_on'] = '记录时间';


$lang['request_details'] = '请求详情';
$lang['raw_content'] = '原始内容';
$lang['headers'] = '头部信息';
$lang['format_type'] = '格式类型';

// Permission section
$lang['show_campaign'] = '显示活动';
$lang['clear_log'] = '清除日志';
$lang['log_activity'] = '记录活动';
$lang['load_template'] = '加载模板';

$lang['action'] = '操作';
$lang['total_parameters'] = '总参数';
$lang['template_name'] = '模板名称';
$lang['log_cleared_successfully'] = '日志成功清除';
$lang['whatsbot_stats'] = 'WhatsBot 统计';

$lang['not_found_or_deleted'] = '未找到或已删除';
$lang['response'] = '响应';

$lang['select_image'] = '选择图片';
$lang['image'] = '图片';
$lang['image_deleted_successfully'] = '图片成功删除';
$lang['whatsbot_settings'] = 'Whatsbot 设置';
$lang['maximum_file_size_should_be'] = '最大文件大小应为 ';
$lang['allowed_file_types'] = '允许的文件类型：';

$lang['send_image'] = '发送图片';
$lang['send_video'] = '发送视频';
$lang['send_document'] = '发送文档';
$lang['record_audio'] = '录音';
$lang['chat_media_info'] = '更多支持的内容类型和后处理媒体大小信息';
$lang['help'] = '帮助';

// v1.1.0
$lang['clone'] = '克隆';
$lang['bot_clone_successfully'] = '机器人成功克隆';
$lang['all_chat'] = '所有聊天';
$lang['from'] = '来自：';
$lang['phone_no'] = '电话：';
$lang['supportagent'] = '支持代理';
$lang['assign_chat_permission_to_support_agent'] = '仅将聊天权限分配给支持代理';
$lang['enable_whatsapp_notification_sound'] = '启用 WhatsApp 聊天通知声音';
$lang['notification_sound'] = '通知声音';
$lang['trigger_keyword'] = '触发关键词';
$lang['modal_title'] = '选择支持代理';
$lang['close_btn'] = '关闭';
$lang['save_btn'] = '保存';
$lang['support_agent'] = '支持代理';
$lang['change_support_agent'] = '更改支持代理';
$lang['replay_message'] = '您不能发送消息，24小时已过。';
$lang['support_agent_note'] = '<strong>注意：</strong>启用支持代理功能时，线索分配人将自动分配到聊天中。管理员也可以从聊天页面分配新代理。';
$lang['permission_bot_clone'] = '克隆机器人';
$lang['remove_chat'] = '移除聊天';
$lang['default_message_on_no_match'] = '默认回复 - 如果没有任何关键词匹配';
$lang['default_message_note'] = '<strong>注意：</strong>启用此选项将增加您的 webhook 负载。有关更多信息，请访问此<a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">链接</a>。';

$lang['whatsbot_connect_account'] = 'Whatsbot 连接账户';
$lang['whatsbot_message_bot'] = 'Whatsbot 消息机器人';
$lang['whatsbot_template_bot'] = 'Whatsbot 模板机器人';
$lang['whatsbot_template'] = 'Whatsbot 模板';
$lang['whatsbot_campaigns'] = 'Whatsbot 活动';
$lang['whatsbot_chat'] = 'Whatsbot 聊天';
$lang['whatsbot_log_activity'] = 'Whatsbot 记录活动';
$lang['message_templates_not_exists_note'] = '元模板权限缺失。请在您的元账户中启用。';

// v1.2.0
$lang['ai_prompt'] = 'AI 提示';
$lang['ai_prompt_note'] = '对于 AI 提示，请输入消息以启用该功能，或使用已启用的 AI 提示';
$lang['emojis'] = '表情符号';
$lang['translate'] = '翻译';
$lang['change_tone'] = '改变语气';
$lang['professional'] = '专业';
$lang['friendly'] = '友好';
$lang['empathetic'] = '同情';
$lang['straightforward'] = '直截了当';
$lang['simplify_language'] = '简化语言';
$lang['fix_spelling_and_grammar'] = '修正拼写和语法';

$lang['ai_integration'] = 'AI 集成';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'OpenAI 密钥 - <a href="https://platform.openai.com/account/api-keys" target="_blank">在哪里找到密钥？</a>';
$lang['chat_text_limit'] = '聊天文本限制';
$lang['chat_text_limit_note'] = '为了优化运营成本，请考虑限制 OpenAI 聊天回复的字数';
$lang['chat_model'] = '聊天模型';
$lang['openai_organizations'] = 'OpenAi 组织';
$lang['template_type'] = '模板类型';
$lang['update'] = '更新';
$lang['open_ai_key_verification_fail'] = 'OpenAi 密钥验证待处理，请从设置中连接您的 OpenAi 账户';
$lang['enable_wb_openai'] = '在聊天中启用 OpenAI';
$lang['webhook_resend_method'] = 'Webhook 重发方法';
$lang['search_language'] = '搜索语言...';
$lang['document'] = '文档';
$lang['select_document'] = '选择文档';
$lang['attchment_deleted_successfully'] = '附件删除成功';
$lang['attach_image_video_docs'] = '附加图片、视频、文档';
$lang['choose_file_type'] = '选择文件类型';
$lang['max_size'] = '最大大小：';

// v1.3.0

// CSV 导入
$lang['bulk_campaigns'] = '批量活动';
$lang['upload_csv'] = '上传 CSV';
$lang['upload'] = '上传';
$lang['csv_uploaded_successfully'] = 'CSV 文件上传成功';
$lang['please_select_file'] = '请选择 CSV 文件';
$lang['phonenumber_field_is_required'] = '电话号码字段是必填项';
$lang['out_of_the'] = '在';
$lang['records_in_your_csv_file'] = '您的 CSV 文件中的记录，';
$lang['valid_the_campaign_can_be_sent'] = '条记录有效。<br /> 这些活动可成功发送给';
$lang['users'] = '用户';
$lang['campaigns_from_csv_file'] = '来自 CSV 文件的活动';
$lang['download_sample'] = '下载样本';
$lang['csv_rule_1'] = '1. <b>电话号码列要求：</b>您的 CSV 文件必须包含名为“Phoneno”的列。此列中的每条记录应包含有效的联系号码，正确格式应包括国家代码和“+”号。<br /><br />';
$lang['csv_rule_2'] = '2. <b>CSV 格式和编码：</b>您的 CSV 数据应遵循指定格式。CSV 文件的第一行必须包含列标题，如示例表所示。确保文件编码为 UTF-8 以防止编码问题。';
$lang['please_upload_valid_csv_file'] = '请上传有效的 CSV 文件';
$lang['please_add_valid_number_in_csv_file'] = '请在 CSV 文件中添加有效的<b>电话号码</b>';
$lang['total_send_campaign_list'] = '发送的活动总数：%s';
$lang['sample_data'] = '示例数据';
$lang['firstname'] = '名字';
$lang['lastname'] = '姓氏';
$lang['phoneno'] = '电话号码';
$lang['email'] = '电子邮件';
$lang['country'] = '国家';
$lang['download_sample_and_read_rules'] = '下载示例文件并阅读规则';
$lang['please_wait_your_request_in_process'] = '请稍等，您的请求正在处理中。';
$lang['whatsbot_bulk_campaign'] = 'Whatsbot 批量活动';
$lang['csv_campaign'] = 'CSV 活动';

// 预设回复
$lang['canned_reply'] = '预设回复';
$lang['canned_reply_menu'] = '预设回复';
$lang['create_canned_reply'] = '创建预设回复';
$lang['title'] = '标题';
$lang['desc'] = '描述';
$lang['public'] = '公开';
$lang['action'] = '操作';
$lang['delete_successfully'] = '回复已删除。';
$lang['cannot_delete'] = '无法删除回复。';
$lang['whatsbot_canned_reply'] = 'Whatsbot 预设回复';
$lang['reply'] = '回复';

// AI 提示
$lang['ai_prompts'] = 'AI 提示';
$lang['create_ai_prompts'] = '创建 AI 提示';
$lang['name'] = '名称';
$lang['action'] = '操作';
$lang['prompt_name'] = '提示名称';
$lang['prompt_action'] = '提示操作';
$lang['whatsbot_ai_prompts'] = 'Whatsbot AI 提示';

// 新聊天
$lang['replying_to'] = '回复：';
$lang['download_document'] = '下载文档';
$lang['custom_prompt'] = '自定义提示';
$lang['canned_replies'] = '预设回复';
$lang['use_@_to_add_merge_fields'] = '使用 “@” 添加合并字段';
$lang['type_your_message'] = '输入您的消息';
$lang['you_cannot_send_a_message_using_this_number'] = '您无法使用此号码发送消息。';

// 机器人流程
$lang['bot_flow'] = '机器人流程';
$lang['create_new_flow'] = '创建新流程';
$lang['flow_name'] = '流程名称';
$lang['flow'] = '流程';
$lang['bot_flow_builder'] = '机器人流程构建器';
$lang['you_can_not_upload_file_type'] = '您无法上传 <b>%s</b> 类型的文件';
$lang['whatsbot_bot_flow'] = 'Whatsbot 机器人流程';

// v1.3.2
$lang['auto_clear_chat_history'] = '自动清除聊天记录';
$lang['enable_auto_clear_chat_history'] = '启用自动清除聊天记录';
$lang['auto_clear_time'] = '自动清除历史记录时间';
$lang['clear_chat_history_note'] = '<strong>注意：</strong>如果启用自动清除聊天记录功能，每当定时任务运行时，聊天记录将根据您指定的天数自动清除。';
$lang['source'] = '来源';
$lang['groups'] = '群组';

// v1.3.3
$lang['click_user_to_chat'] = '点击用户进行聊天';
$lang['searching'] = '搜索中...';
$lang['filters'] = '过滤器';
$lang['relation_type'] = '关系类型';
$lang['groups'] = '群组';
$lang['source'] = '来源';
$lang['status'] = '状态';
$lang['select_type'] = '选择类型';
$lang['select_agents'] = '选择代理';
$lang['select_group'] = '选择组';
$lang['select_source'] = '选择来源';
$lang['select_status'] = '选择状态';
$lang['agents'] = '代理';

// v1.4.2
$lang['read_only'] = '只读';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';

$lang['send_location'] = 'Send Location';
$lang['search_for_a_place'] = 'Search for a place';
$lang['address_optional'] = 'Address (optional)';
$lang['latitude'] = 'Latitude';
$lang['longitude'] = 'Longitude';
$lang['drag_the_maker_to_set_the_location'] = 'Drag the marker to set the location or search for a place.';
$lang['use_my_location'] = 'Use My Current Location';
$lang['send_location'] = 'Send Location';

$lang['initiate_chat'] = 'Initiate Chat';
$lang['please_select_at_least_one_lead'] = 'Please select at least one lead';
$lang['chat_initiated_successfully'] = 'Chat Initiated  successfuly';
$lang['something_went_wrong'] = "Something Went Wrong";

$lang['video'] = "Video";
$lang['select_video'] = "Select Video";

$lang['whatsbot_cron'] = 'Whatsbot Cron';

// Session Management
$lang['session_management']                 = 'WhatsApp Session Management';
$lang['enable_session_management']          = 'Enable session management';
$lang['session_management_help_text']       = 'WhatsApp Cloud API has a 24-hour session limit for business-initiated messages. Enable this to send a reminder before the session expires.';
$lang['session_expiry_message']             = 'Session expiry message';
$lang['session_expiry_message_help']        = 'This message will be sent to customers before their 24-hour session expires.';
$lang['session_expiry_hours']               = 'Hours before expiry to send reminder';
$lang['session_expiry_hours_help']          = 'Set how many hours after the last customer message to send the reminder (max 23 hours).';
$lang['include_session_reset_button']       = 'Include quick reply button';
$lang['include_session_reset_button_help']  = 'Add a quick reply button to make it easier for customers to respond.';
$lang['session_management_note']            = 'Note: This feature requires the cron job to be properly configured. The cron job should run at least once per hour.';
$lang['session_reset_button_text']          = 'Continue Conversation';
$lang['whatsapp_session_management']        = 'WhatsApp Session Management';
$lang['active_sessions']                    = 'Active Sessions';
$lang['expiring_sessions']                  = 'Expiring Sessions';
$lang['sessions_reset_today']               = 'Sessions Reset Today';
$lang['sessions_reset_week']                = 'Sessions Reset (7 Days)';
$lang['session_management_disabled']        = 'WhatsApp Session Management is currently disabled.';

// Dashboard and Reports
$lang['session_reset_count']                = 'Session Reset Count';
$lang['session_expired']                    = 'Session Expired';
$lang['session_active']                     = 'Session Active';
$lang['session_reset_success_rate']         = 'Reset Success Rate';
$lang['customer_responded']                 = 'Customer Responded';
$lang['customer_not_responded']             = 'Customer Did Not Respond';
$lang['enable_now']                         = 'Enable Now';
