<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'アカウントを接続';
$lang['connect_whatsapp_business'] = 'WhatsAppビジネスを接続';
$lang['campaigning'] = 'キャンペーン';
$lang['business_account_id_description'] = 'あなたのWhatsAppビジネスアカウント(WABA) ID';
$lang['access_token_description'] = 'Facebook開発者ポータルでアカウントを作成後のユーザーアクセストークン';
$lang['whatsapp_business_account_id'] = 'WhatsAppビジネスアカウントID';
$lang['whatsapp_access_token'] = 'WhatsAppアクセストークン';
$lang['webhook_callback_url'] = 'WebhookコールバックURL';
$lang['verify_token'] = 'トークンを検証';
$lang['connect'] = '接続';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'ワンクリックアカウント接続';
$lang['connect_your_whatsapp_account'] = 'あなたのWhatsAppアカウントを接続';
$lang['copy'] = 'コピー';
$lang['copied'] = 'コピーしました!!';
$lang['disconnect'] = '切断';
$lang['number'] = '番号';
$lang['number_id'] = '番号ID';
$lang['quality'] = '品質';
$lang['status'] = 'ステータス';
$lang['business_account_id'] = 'ビジネスアカウントID';
$lang['permissions'] = '権限';
$lang['phone_number_id_description'] = 'WhatsAppビジネスAPIに接続された電話番号のID。わからない場合は、GET電話番号IDリクエストを使用してWhatsApp APIから取得できます (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'WhatsApp登録電話の番号ID';
$lang['update_details'] = '詳細を更新';

$lang['bots'] = 'ボット';
$lang['bots_management'] = 'ボット管理';
$lang['create_template_base_bot'] = 'テンプレートベースボットを作成';
$lang['create_message_bot'] = 'メッセージボットを作成';
$lang['type'] = 'タイプ';
$lang['message_bot'] = 'メッセージボット';
$lang['new_template_bot'] = '新しいテンプレートボット';
$lang['new_message_bot'] = '新しいメッセージボット';
$lang['bot_name'] = 'ボット名';
$lang['reply_text'] = '返信テキスト <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="リードまたは連絡先に送信されるテキスト。{companyname}、{crm_url}やリードまたは連絡先の任意のカスタムマージフィールド、または\'@\'を使用して利用可能なマージフィールドを見つけることもできます。" data-placement="bottom"></i> <span class="text-muted">(最大文字数は1024です)</span>';
$lang['reply_type'] = '返信タイプ';
$lang['trigger'] = 'トリガー';
$lang['header'] = 'ヘッダー';
$lang['footer_bot'] = 'フッター <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="最大文字数は60です" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'オプション1: 返信ボタン付きボット';
$lang['bot_with_button_link'] = 'オプション2: ボタンリンク付きボット - CTA URL';
$lang['button1'] = 'ボタン1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="最大文字数は20です" data-placement="bottom"></i>';
$lang['button1_id'] = 'ボタン1 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="最大文字数は256です" data-placement="bottom"></i>';
$lang['button2'] = 'ボタン2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="最大文字数は20です" data-placement="bottom"></i>';
$lang['button2_id'] = 'ボタン2 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="最大文字数は256です" data-placement="bottom"></i>';
$lang['button3'] = 'ボタン3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="最大文字数は20です" data-placement="bottom"></i>';
$lang['button3_id'] = 'ボタン3 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="最大文字数は256です" data-placement="bottom"></i>';
$lang['button_name'] = 'ボタン名 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="最大文字数は20です" data-placement="bottom"></i>';
$lang['button_link'] = 'ボタンリンク';
$lang['enter_name'] = '名前を入力';
$lang['select_reply_type'] = '返信タイプを選択';
$lang['enter_bot_reply_trigger'] = 'ボットの返信トリガーを入力';
$lang['enter_header'] = 'ヘッダーを入力';
$lang['enter_footer'] = 'フッターを入力';
$lang['enter_button1'] = 'ボタン1を入力';
$lang['enter_button1_id'] = 'ボタン1 IDを入力';
$lang['enter_button2'] = 'ボタン2を入力';
$lang['enter_button2_id'] = 'ボタン2 IDを入力';
$lang['enter_button3'] = 'ボタン3を入力';
$lang['enter_button3_id'] = 'ボタン3 IDを入力';
$lang['enter_button_name'] = 'ボタン名を入力';
$lang['enter_button_url'] = 'ボタンのURLを入力';
$lang['on_exact_match'] = '返信ボット: 完全一致時';
$lang['when_message_contains'] = '返信ボット: メッセージに含まれる場合';
$lang['when_client_send_the_first_message'] = 'ウェルカム返信 - リードまたはクライアントが最初のメッセージを送信したとき';
$lang['bot_create_successfully'] = 'ボットが正常に作成されました';
$lang['bot_update_successfully'] = 'ボットが正常に更新されました';
$lang['bot_deleted_successfully'] = 'ボットが正常に削除されました';
$lang['templates'] = 'テンプレート';
$lang['template_data_loaded'] = 'テンプレートが正常にロードされました';
$lang['load_templates'] = 'テンプレートをロード';
$lang['template_management'] = 'テンプレート管理';


// campaigns
$lang['campaign'] = 'キャンペーン';
$lang['campaigns'] = 'キャンペーン';
$lang['send_new_campaign'] = '新しいキャンペーンを送信';
$lang['campaign_name'] = 'キャンペーン名';
$lang['template'] = 'テンプレート';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="顧客ごとに、連絡先のタイムゾーンに基づいて" data-placement="left"></i>スケジュール送信時間';
$lang['scheduled_time_description'] = '顧客ごとに、連絡先のタイムゾーンに基づいて';
$lang['ignore_scheduled_time_and_send_now'] = 'スケジュール時間を無視して今すぐ送信';
$lang['template'] = 'テンプレート';
$lang['leads'] = 'リード';
$lang['delivered_to'] = '配信先';
$lang['read_by'] = '読まれた';
$lang['variables'] = '変数';
$lang['body'] = '本文';
$lang['variable'] = '変数';
$lang['match_with_selected_field'] = '選択されたフィールドと一致';
$lang['preview'] = 'プレビュー';
$lang['send_campaign'] = 'キャンペーンを送信';
$lang['send_to'] = '送信先';
$lang['send_campaign'] = 'キャンペーンを送信';
$lang['view_campaign'] = 'キャンペーンを表示';
$lang['campaign_daily_task'] = 'キャンペーン日次タスク';
$lang['back'] = '戻る';
$lang['phone'] = '電話';
$lang['message'] = 'メッセージ';
$lang['currently_type_not_supported'] = '現在、<strong> %s </strong>テンプレートタイプはサポートされていません！';
$lang['of_your'] = 'あなたの';
$lang['contacts'] = '連絡先';
$lang['select_all_leads'] = 'すべてのリードを選択';
$lang['select_all_note_leads'] = 'これを選択すると、今後のすべてのリードがこのキャンペーンに含まれます。';
$lang['select_all_note_contacts'] = 'これを選択すると、今後のすべての連絡先がこのキャンペーンに含まれます。';

$lang['verified_name'] = '確認済み名';
$lang['mark_as_default'] = 'デフォルトとしてマーク';
$lang['default_number_updated'] = 'デフォルト電話番号IDが正常に更新されました';
$lang['currently_using_this_number'] = '現在この番号を使用中';
$lang['leads'] = 'リード';
$lang['pause_campaign'] = 'キャンペーンを一時停止';
$lang['resume_campaign'] = 'キャンペーンを再開';
$lang['campaign_resumed'] = 'キャンペーンが再開されました';
$lang['campaign_paused'] = 'キャンペーンが一時停止されました';

//Template
$lang['body_data'] = '本文データ';
$lang['category'] = 'カテゴリー';

// Template bot
$lang['create_new_template_bot'] = '新しいテンプレートボットを作成';
$lang['template_bot'] = 'テンプレートボット';
$lang['variables'] = '変数';
$lang['preview'] = 'プレビュー';
$lang['template'] = 'テンプレート';
$lang['bot_content_1'] = 'このメッセージは、連絡先から送信されたメッセージでトリガールールが満たされたときに送信されます。';
$lang['save_bot'] = 'ボットを保存';
$lang['please_select_template'] = 'テンプレートを選択してください';
$lang['use_manually_define_value'] = '手動で定義された値を使用';
$lang['merge_fields'] = 'マージフィールド';
$lang['template_bot_create_successfully'] = 'テンプレートボットが正常に作成されました';
$lang['template_bot_update_successfully'] = 'テンプレートボットが正常に更新されました';
$lang['text_bot'] = 'テキストボット';
$lang['option_2_bot_with_link'] = 'オプション2：リンク付きボット - 行動を促す（CTA）URL';
$lang['option_3_file'] = 'オプション3：ファイル付きボット';
// Bot settings
$lang['bot'] = 'ボット';
$lang['bot_delay_response'] = '応答遅延が予想される場合に送信されるメッセージ';
$lang['bot_delay_response_placeholder'] = '少々お待ちください、すぐにお答えします';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = '関係タイプ';
$lang['select_all'] = 'すべて選択';
$lang['total'] = '合計';
$lang['merge_field_note'] = '\'@\'を使用してマージフィールドを追加します。';
$lang['send_to_all'] = 'すべてに送信';
$lang['or'] = 'または';

$lang['convert_whatsapp_message_to_lead'] = '新しいリードを自動的に取得（新しいWhatsAppメッセージをリードに変換）';
$lang['leads_status'] = 'リードのステータス';
$lang['leads_assigned'] = 'リードが割り当てられました';
$lang['whatsapp_auto_lead'] = 'WhatsApp自動リード';
$lang['webhooks_label'] = 'WhatsAppで受信したデータが再送信されます';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'WebHooks再送信を有効にする';
$lang['chat'] = 'チャット';
$lang['black_listed_phone_numbers'] = 'ブラックリストに載っている電話番号';
$lang['sent_status'] = '送信ステータス';

$lang['active'] = 'アクティブ';
$lang['approved'] = '承認済み';
$lang['this_month'] = '今月';
$lang['open_chats'] = 'オープンチャット';
$lang['resolved_conversations'] = '解決済みの会話';
$lang['messages_sent'] = '送信されたメッセージ';
$lang['account_connected'] = 'アカウントが接続されました';
$lang['account_disconnected'] = 'アカウントが切断されました';
$lang['webhook_verify_token'] = 'Webhook確認トークン';
// Chat integration
$lang['chat_message_note'] = 'メッセージはすぐに送信されます。新しい連絡先の場合、連絡先があなたと対話を開始するまでこのリストには表示されませんのでご注意ください。';

$lang['activity_log'] = 'アクティビティログ';
$lang['whatsapp_logs'] = 'WhatsAppログ';
$lang['response_code'] = 'レスポンスコード';
$lang['recorded_on'] = '記録日';

$lang['request_details'] = 'リクエストの詳細';
$lang['raw_content'] = '生のコンテンツ';
$lang['headers'] = 'ヘッダー';
$lang['format_type'] = 'フォーマットタイプ';

// Permission section
$lang['show_campaign'] = 'キャンペーンを表示';
$lang['clear_log'] = 'ログをクリア';
$lang['log_activity'] = 'アクティビティをログに記録';
$lang['load_template'] = 'テンプレートを読み込む';

$lang['action'] = 'アクション';
$lang['total_parameters'] = '総パラメータ';
$lang['template_name'] = 'テンプレート名';
$lang['log_cleared_successfully'] = 'ログが正常にクリアされました';
$lang['whatsbot_stats'] = 'WhatsBot統計';

$lang['not_found_or_deleted'] = '見つからないか削除されました';
$lang['response'] = 'レスポンス';

$lang['select_image'] = '画像を選択';
$lang['image'] = '画像';
$lang['image_deleted_successfully'] = '画像が正常に削除されました';
$lang['whatsbot_settings'] = 'Whatsbot設定';
$lang['maximum_file_size_should_be'] = '最大ファイルサイズは次のとおりです';
$lang['allowed_file_types'] = '許可されているファイルタイプ：';

$lang['send_image'] = '画像を送信';
$lang['send_video'] = '動画を送信';
$lang['send_document'] = '文書を送信';
$lang['record_audio'] = '音声を録音';
$lang['chat_media_info'] = 'サポートされているコンテンツタイプとメディアサイズのポストプロセッシングに関する詳細';
$lang['help'] = 'ヘルプ';


// v1.1.0
$lang['clone'] = 'クローン';
$lang['bot_clone_successfully'] = 'ボットが正常にクローンされました';
$lang['all_chat'] = 'すべてのチャット';
$lang['from'] = '送信元:';
$lang['phone_no'] = '電話番号:';
$lang['supportagent'] = 'サポートエージェント';
$lang['assign_chat_permission_to_support_agent'] = 'サポートエージェントにチャットの権限を割り当てる';
$lang['enable_whatsapp_notification_sound'] = 'WhatsAppチャット通知音を有効にする';
$lang['notification_sound'] = '通知音';
$lang['trigger_keyword'] = 'トリガーキーワード';
$lang['modal_title'] = 'サポートエージェントを選択';
$lang['close_btn'] = '閉じる';
$lang['save_btn'] = '保存';
$lang['support_agent'] = 'サポートエージェント';
$lang['change_support_agent'] = 'サポートエージェントを変更';
$lang['replay_message'] = '24時間経過したため、メッセージを送信できません。';
$lang['support_agent_note'] = '<strong>注:</strong> サポートエージェント機能を有効にすると、リードの担当者が自動的にチャットに割り当てられます。管理者はチャットページから新しいエージェントを割り当てることもできます。';
$lang['permission_bot_clone'] = 'ボットをクローンする';
$lang['remove_chat'] = 'チャットを削除';
$lang['default_message_on_no_match'] = 'デフォルトの返信 - キーワードが一致しない場合';
$lang['default_message_note'] = '<strong>注:</strong> このオプションを有効にすると、Webhookの負荷が増加します。詳細については、この<a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">リンク</a>をご覧ください。';

$lang['whatsbot_connect_account'] = 'Whatsbotアカウントを接続';
$lang['whatsbot_message_bot'] = 'Whatsbotメッセージボット';
$lang['whatsbot_template_bot'] = 'Whatsbotテンプレートボット';
$lang['whatsbot_template'] = 'Whatsbotテンプレート';
$lang['whatsbot_campaigns'] = 'Whatsbotキャンペーン';
$lang['whatsbot_chat'] = 'Whatsbotチャット';
$lang['whatsbot_log_activity'] = 'Whatsbotログアクティビティ';
$lang['message_templates_not_exists_note'] = 'メタテンプレートの権限がありません。Metaアカウントで有効にしてください。';

// v1.2.0
$lang['ai_prompt'] = 'AIプロンプト';
$lang['ai_prompt_note'] = 'AIプロンプトの場合、機能を有効にするメッセージを入力するか、すでに有効になっている場合はAIプロンプトを使用してください';
$lang['emojis'] = '絵文字';
$lang['translate'] = '翻訳';
$lang['change_tone'] = 'トーンを変更';
$lang['professional'] = 'プロフェッショナル';
$lang['friendly'] = 'フレンドリー';
$lang['empathetic'] = '共感的';
$lang['straightforward'] = '率直';
$lang['simplify_language'] = '言語を簡略化';
$lang['fix_spelling_and_grammar'] = 'スペルと文法を修正';

$lang['ai_integration'] = 'AI統合';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'OpenAI秘密鍵 - <a href="https://platform.openai.com/account/api-keys" target="_blank">秘密鍵を見つける場所</a>';
$lang['chat_text_limit'] = 'チャットテキストの制限';
$lang['chat_text_limit_note'] = '運用コストを最適化するために、OpenAIのチャット応答の単語数を制限することを検討してください';
$lang['chat_model'] = 'チャットモデル';
$lang['openai_organizations'] = 'OpenAI組織';
$lang['template_type'] = 'テンプレートタイプ';
$lang['update'] = '更新';
$lang['open_ai_key_verification_fail'] = 'OpenAIキーの検証が保留中です。設定からOpenAIアカウントを接続してください';
$lang['enable_wb_openai'] = 'チャットでOpenAIを有効にする';
$lang['webhook_resend_method'] = 'Webhook再送信方法';
$lang['search_language'] = '言語を検索...';
$lang['document'] = 'ドキュメント';
$lang['select_document'] = 'ドキュメントを選択';
$lang['attchment_deleted_successfully'] = '添付ファイルが正常に削除されました';
$lang['attach_image_video_docs'] = '画像、ビデオ、ドキュメントを添付';
$lang['choose_file_type'] = 'ファイルタイプを選択';
$lang['max_size'] = '最大サイズ: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'バルクキャンペーン';
$lang['upload_csv'] = 'CSVをアップロード';
$lang['upload'] = 'アップロード';
$lang['csv_uploaded_successfully'] = 'CSVファイルが正常にアップロードされました';
$lang['please_select_file'] = 'CSVファイルを選択してください';
$lang['phonenumber_field_is_required'] = '電話番号フィールドは必須です';
$lang['out_of_the'] = 'の外';
$lang['records_in_your_csv_file'] = 'CSVファイルのレコード、';
$lang['valid_the_campaign_can_be_sent'] = 'レコードが有効です。<br />キャンペーンはこれらに正常に送信できます';
$lang['users'] = 'ユーザー';
$lang['campaigns_from_csv_file'] = 'CSVファイルからのキャンペーン';
$lang['download_sample'] = 'サンプルをダウンロード';
$lang['csv_rule_1'] = '1. <b>電話番号列の要件:</b> CSVファイルには「Phoneno」と名付けられた列が含まれている必要があります。この列の各レコードには、国コードを含む有効な連絡先番号が正しくフォーマットされている必要があります。<br /><br />';
$lang['csv_rule_2'] = '2. <b>CSV形式とエンコーディング:</b> CSVデータは指定された形式に従っている必要があります。CSVファイルの最初の行には、例の表に示されたように列のヘッダーが含まれている必要があります。ファイルはUTF-8でエンコードされていることを確認し、エンコーディングの問題を防ぎます。';
$lang['please_upload_valid_csv_file'] = '有効なCSVファイルをアップロードしてください';
$lang['please_add_valid_number_in_csv_file'] = 'CSVファイルに有効な<b>Phoneno</b>を追加してください';
$lang['total_send_campaign_list'] = '送信されたキャンペーンの合計: %s';
$lang['sample_data'] = 'サンプルデータ';
$lang['firstname'] = '名';
$lang['lastname'] = '姓';
$lang['phoneno'] = '電話番号';
$lang['email'] = 'メール';
$lang['country'] = '国';
$lang['download_sample_and_read_rules'] = 'サンプルファイルをダウンロードし、ルールを読む';
$lang['please_wait_your_request_in_process'] = 'お待ちください。リクエストは現在処理中です。';
$lang['whatsbot_bulk_campaign'] = 'Whatsbotバルクキャンペーン';
$lang['csv_campaign'] = 'CSVキャンペーン';

// Canned reply
$lang['canned_reply'] = '定型返信';
$lang['canned_reply_menu'] = '定型返信';
$lang['create_canned_reply'] = '定型返信を作成';
$lang['title'] = 'タイトル';
$lang['desc'] = '説明';
$lang['public'] = '公開';
$lang['action'] = 'アクション';
$lang['delete_successfully'] = '返信が削除されました。';
$lang['cannot_delete'] = '返信を削除できません。';
$lang['whatsbot_canned_reply'] = 'Whatsbot定型返信';
$lang['reply'] = '返信';

//AI Prompts
$lang['ai_prompts'] = 'AIプロンプト';
$lang['create_ai_prompts'] = 'AIプロンプトを作成';
$lang['name'] = '名前';
$lang['action'] = 'アクション';
$lang['prompt_name'] = 'プロンプト名';
$lang['prompt_action'] = 'プロンプトアクション';
$lang['whatsbot_ai_prompts'] = 'Whatsbot AIプロンプト';

// new chat
$lang['replying_to'] = '返信中:';
$lang['download_document'] = 'ドキュメントをダウンロード';
$lang['custom_prompt'] = 'カスタムプロンプト';
$lang['canned_replies'] = '定型返信';
$lang['use_@_to_add_merge_fields'] = 'マージフィールドを追加するには\'@\'を使用';
$lang['type_your_message'] = 'メッセージを入力';
$lang['you_cannot_send_a_message_using_this_number'] = 'この番号を使用してメッセージを送信できません。';

// bot flow
$lang['bot_flow'] = 'ボットフロー';
$lang['create_new_flow'] = '新しいフローを作成';
$lang['flow_name'] = 'フロー名';
$lang['flow'] = 'フロー';
$lang['bot_flow_builder'] = 'ボットフロービルダー';
$lang['you_can_not_upload_file_type'] = 'この<b>%s</b>タイプのファイルをアップロードできません';
$lang['whatsbot_bot_flow'] = 'Whatsbotボットフロー';

// v1.3.2
$lang['auto_clear_chat_history'] = 'チャット履歴の自動クリア';
$lang['enable_auto_clear_chat_history'] = 'チャット履歴の自動クリアを有効にする';
$lang['auto_clear_time'] = '自動クリアの時間';
$lang['clear_chat_history_note'] = '<strong>注:</strong> 自動クリアチャット履歴機能を有効にすると、指定された日数に基づいて、cronジョブが実行されるたびにチャット履歴が自動的にクリアされます。';
$lang['source'] = 'ソース';
$lang['groups'] = 'グループ';

// v1.3.3
$lang['click_user_to_chat'] = 'ユーザーをクリックしてチャット';
$lang['searching'] = '検索中...';
$lang['filters'] = 'フィルタ';
$lang['relation_type'] = '関係の種類';
$lang['groups'] = 'グループ';
$lang['source'] = 'ソース';
$lang['status'] = 'ステータス';
$lang['select_type'] = 'タイプを選択';
$lang['select_agents'] = 'エージェントを選択';
$lang['select_group'] = 'グループを選択';
$lang['select_source'] = 'ソースを選択';
$lang['select_status'] = 'ステータスを選択';
$lang['agents'] = 'エージェント';

// v1.4.2
$lang['read_only'] = '読み取り専用';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';

$lang['send_location'] = 'Send Location';
$lang['search_for_a_place'] = 'Search for a place';
$lang['address_optional'] = 'Address (optional)';
$lang['latitude'] = 'Latitude';
$lang['longitude'] = 'Longitude';
$lang['drag_the_maker_to_set_the_location'] = 'Drag the marker to set the location or search for a place.';
$lang['use_my_location'] = 'Use My Current Location';
$lang['send_location'] = 'Send Location';

$lang['initiate_chat'] = 'Initiate Chat';
$lang['please_select_at_least_one_lead'] = 'Please select at least one lead';
$lang['chat_initiated_successfully'] = 'Chat Initiated  successfuly';
$lang['something_went_wrong'] = "Something Went Wrong";

$lang['video'] = "Video";
$lang['select_video'] = "Select Video";

$lang['whatsbot_cron'] = 'Whatsbot Cron';

// Session Management
$lang['session_management']                 = 'WhatsApp Session Management';
$lang['enable_session_management']          = 'Enable session management';
$lang['session_management_help_text']       = 'WhatsApp Cloud API has a 24-hour session limit for business-initiated messages. Enable this to send a reminder before the session expires.';
$lang['session_expiry_message']             = 'Session expiry message';
$lang['session_expiry_message_help']        = 'This message will be sent to customers before their 24-hour session expires.';
$lang['session_expiry_hours']               = 'Hours before expiry to send reminder';
$lang['session_expiry_hours_help']          = 'Set how many hours after the last customer message to send the reminder (max 23 hours).';
$lang['include_session_reset_button']       = 'Include quick reply button';
$lang['include_session_reset_button_help']  = 'Add a quick reply button to make it easier for customers to respond.';
$lang['session_management_note']            = 'Note: This feature requires the cron job to be properly configured. The cron job should run at least once per hour.';
$lang['session_reset_button_text']          = 'Continue Conversation';
$lang['whatsapp_session_management']        = 'WhatsApp Session Management';
$lang['active_sessions']                    = 'Active Sessions';
$lang['expiring_sessions']                  = 'Expiring Sessions';
$lang['sessions_reset_today']               = 'Sessions Reset Today';
$lang['sessions_reset_week']                = 'Sessions Reset (7 Days)';
$lang['session_management_disabled']        = 'WhatsApp Session Management is currently disabled.';

// Dashboard and Reports
$lang['session_reset_count']                = 'Session Reset Count';
$lang['session_expired']                    = 'Session Expired';
$lang['session_active']                     = 'Session Active';
$lang['session_reset_success_rate']         = 'Reset Success Rate';
$lang['customer_responded']                 = 'Customer Responded';
$lang['customer_not_responded']             = 'Customer Did Not Respond';
$lang['enable_now']                         = 'Enable Now';
