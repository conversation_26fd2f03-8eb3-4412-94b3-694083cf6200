<?php

defined('BASEPATH') || exit('No direct script access allowed');

// hesap bağlama
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Hesabı Bağla';
$lang['connect_whatsapp_business'] = 'WhatsApp Business Bağla';
$lang['campaigning'] = 'Kampanya Yönetimi';
$lang['business_account_id_description'] = 'WhatsApp Business Hesap (WABA) Kimliğiniz';
$lang['access_token_description'] = 'Facebook Developers Portal\'a kaydolduktan sonra aldığınız Kullanıcı Erişim Jetonunuz';
$lang['whatsapp_business_account_id'] = 'WhatsApp Business Hesap Kimliği';
$lang['whatsapp_access_token'] = 'WhatsApp Erişim Jetonu';
$lang['webhook_callback_url'] = 'Webhook Geri Çağırma URL\'si';
$lang['verify_token'] = 'Token\'ı Doğrula';
$lang['connect'] = 'Bağlan';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'Tek Tıklamayla Hesap Bağlantısı';
$lang['connect_your_whatsapp_account'] = 'WhatsApp Hesabınızı Bağlayın';
$lang['copy'] = 'Kopyala';
$lang['copied'] = 'Kopyalandı!!';
$lang['disconnect'] = 'Bağlantıyı Kes';
$lang['number'] = 'Numara';
$lang['number_id'] = 'Numara Kimliği';
$lang['quality'] = 'Kalite';
$lang['status'] = 'Durum';
$lang['business_account_id'] = 'İşletme Hesap Kimliği';
$lang['permissions'] = 'İzinler';
$lang['phone_number_id_description'] = 'WhatsApp Business API\'sine bağlı telefon numarasının kimliği. Emin değilseniz, WhatsApp API\'sinden GET Telefon Numarası Kimliği isteği ile alabilirsiniz ( https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers )';
$lang['phone_number_id'] = 'WhatsApp Kayıtlı Telefon Numarasının Kimliği';
$lang['update_details'] = 'Ayrıntıları Güncelle';

$lang['bots'] = 'Botlar';
$lang['bots_management'] = 'Bot Yönetimi';
$lang['create_template_base_bot'] = 'Şablon Tabanlı Bot Oluştur';
$lang['create_message_bot'] = 'Mesaj Botu Oluştur';
$lang['type'] = 'Tür';
$lang['message_bot'] = 'Mesaj Botu';
$lang['new_template_bot'] = 'Yeni Şablon Botu';
$lang['new_message_bot'] = 'Yeni Mesaj Botu';
$lang['bot_name'] = 'Bot Adı';
$lang['reply_text'] = 'Cevap metni <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Lead veya kişi ile gönderilecek metin. Ayrıca {companyname}, {crm_url} veya diğer özel alanları kullanabilir ya da \'@\' işareti ile kullanılabilir alanları bulabilirsiniz" data-placement="bottom"></i> <span class="text-muted">(En fazla 1024 karakter izin verilir)</span>';
$lang['reply_type'] = 'Cevap Türü';
$lang['trigger'] = 'Tetikleyici';
$lang['header'] = 'Başlık';
$lang['footer_bot'] = 'Altbilgi <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="En fazla 60 karakter izin verilir" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Seçenek 1: Cevap Butonları ile Bot';
$lang['bot_with_button_link'] = 'Seçenek 2: Düğme Bağlantılı Bot - CTA URL';
$lang['button1'] = 'Buton1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="En fazla 20 karakter izin verilir" data-placement="bottom"></i>';
$lang['button1_id'] = 'Buton1 Kimliği <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="En fazla 256 karakter izin verilir" data-placement="bottom"></i>';
$lang['button2'] = 'Buton2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="En fazla 20 karakter izin verilir" data-placement="bottom"></i>';
$lang['button2_id'] = 'Buton2 Kimliği <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="En fazla 256 karakter izin verilir" data-placement="bottom"></i>';
$lang['button3'] = 'Buton3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="En fazla 20 karakter izin verilir" data-placement="bottom"></i>';
$lang['button3_id'] = 'Buton3 Kimliği <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="En fazla 256 karakter izin verilir" data-placement="bottom"></i>';
$lang['button_name'] = 'Buton Adı <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="En fazla 20 karakter izin verilir" data-placement="bottom"></i>';
$lang['button_link'] = 'Buton Bağlantısı';
$lang['enter_name'] = 'Adı Girin';
$lang['select_reply_type'] = 'Cevap Türünü Seçin';
$lang['enter_bot_reply_trigger'] = 'Bot Cevap Tetikleyicisini Girin';
$lang['enter_header'] = 'Başlığı Girin';
$lang['enter_footer'] = 'Altbilgiyi Girin';
$lang['enter_button1'] = 'Buton1\'i Girin';
$lang['enter_button1_id'] = 'Buton1 Kimliğini Girin';
$lang['enter_button2'] = 'Buton2\'yi Girin';
$lang['enter_button2_id'] = 'Buton2 Kimliğini Girin';
$lang['enter_button3'] = 'Buton3\'ü Girin';
$lang['enter_button3_id'] = 'Buton3 Kimliğini Girin';
$lang['enter_button_name'] = 'Buton Adını Girin';
$lang['enter_button_url'] = 'Buton URL\'sini Girin';
$lang['on_exact_match'] = 'Tam Eşleşmede Cevap Botu';
$lang['when_message_contains'] = 'Mesaj İçerdiğinde Cevap Botu';
$lang['when_client_send_the_first_message'] = 'Hoş Geldiniz Yanıtı - Müşteri İlk Mesajı Gönderdiğinde';
$lang['bot_create_successfully'] = 'Bot Başarıyla Oluşturuldu';
$lang['bot_update_successfully'] = 'Bot Başarıyla Güncellendi';
$lang['bot_deleted_successfully'] = 'Bot Başarıyla Silindi';
$lang['templates'] = 'Şablonlar';
$lang['template_data_loaded'] = 'Şablon Verileri Başarıyla Yüklendi';
$lang['load_templates'] = 'Şablonları Yükle';
$lang['template_management'] = 'Şablon Yönetimi';

// campaigns
$lang['campaign'] = 'Kampanya';
$lang['campaigns'] = 'Kampanyalar';
$lang['send_new_campaign'] = 'Yeni Kampanya Gönder';
$lang['campaign_name'] = 'Kampanya Adı';
$lang['template'] = 'Şablon';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Müşteri başına, iletişim zaman dilimine göre" data-placement="left"></i>Planlı Gönderim Zamanı';
$lang['scheduled_time_description'] = 'Müşteri başına, iletişim zaman dilimine göre';
$lang['ignore_scheduled_time_and_send_now'] = 'Planlı zamanı yok say ve hemen gönder';
$lang['template'] = 'Şablon';
$lang['leads'] = 'Potansiyel Müşteriler';
$lang['delivered_to'] = 'Gönderildi';
$lang['read_by'] = 'Okuyan';
$lang['variables'] = 'Değişkenler';
$lang['body'] = 'Gövde';
$lang['variable'] = 'Değişken';
$lang['match_with_selected_field'] = 'Seçilen alan ile eşleş';
$lang['preview'] = 'Önizleme';
$lang['send_campaign'] = 'Kampanya Gönder';
$lang['send_to'] = 'Gönderilecek';
$lang['send_campaign'] = 'Kampanya Gönder';
$lang['view_campaign'] = 'Kampanyayı Görüntüle';
$lang['campaign_daily_task'] = 'Kampanya günlük görevi';
$lang['back'] = 'Geri';
$lang['phone'] = 'Telefon';
$lang['message'] = 'Mesaj';
$lang['currently_type_not_supported'] = 'Şu anda <strong> %s </strong> şablon türü desteklenmiyor!';
$lang['of_your'] = 'hesabınızdaki ';
$lang['contacts'] = 'İletişim';
$lang['select_all_leads'] = 'Tüm Potansiyel Müşterileri Seç';
$lang['select_all_note_leads'] = 'Bunu seçerseniz, tüm gelecekteki potansiyel müşteriler bu kampanyaya dahil edilir.';
$lang['select_all_note_contacts'] = 'Bunu seçerseniz, tüm gelecekteki iletişimler bu kampanyaya dahil edilir.';

$lang['verified_name'] = 'Doğrulanmış İsim';
$lang['mark_as_default'] = 'Varsayılan olarak işaretle';
$lang['default_number_updated'] = 'Varsayılan telefon numarası başarıyla güncellendi';
$lang['currently_using_this_number'] = 'Şu anda bu numarayı kullanıyorsunuz';
$lang['leads'] = 'Potansiyel Müşteriler';
$lang['pause_campaign'] = 'Kampanyayı Duraklat';
$lang['resume_campaign'] = 'Kampanyayı Devam Ettir';
$lang['campaign_resumed'] = 'Kampanya devam ettirildi';
$lang['campaign_paused'] = 'Kampanya duraklatıldı';

//Template
$lang['body_data'] = 'Gövde Verileri';
$lang['category'] = 'Kategori';

// Template bot
$lang['create_new_template_bot'] = 'Yeni şablon botu oluştur';
$lang['template_bot'] = 'Şablon Botu';
$lang['variables'] = 'Değişkenler';
$lang['preview'] = 'Önizleme';
$lang['template'] = 'Şablon';
$lang['bot_content_1'] = 'Bu mesaj, kontak tarafından gönderilen mesajda tetikleyici kural karşılandığında kontağa gönderilecektir.';
$lang['save_bot'] = 'Botu Kaydet';
$lang['please_select_template'] = 'Lütfen bir şablon seçin';
$lang['use_manually_define_value'] = 'Manuel olarak tanımlanan değeri kullan';
$lang['merge_fields'] = 'Birleştirme Alanları';
$lang['template_bot_create_successfully'] = 'Şablon botu başarıyla oluşturuldu';
$lang['template_bot_update_successfully'] = 'Şablon botu başarıyla güncellendi';
$lang['text_bot'] = 'Metin botu';
$lang['option_2_bot_with_link'] = 'Seçenek 2: Buton bağlantılı bot - Eylem Çağrısı (CTA) URL\'si';
$lang['option_3_file'] = 'Seçenek 3: Dosyası olan bot';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Yanıt beklenirken gönderilecek mesaj';
$lang['bot_delay_response_placeholder'] = 'Bir dakika bekleyin, cevabı hemen vereceğim';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'İlişki Türü';
$lang['select_all'] = 'Hepsini Seç';
$lang['total'] = 'Toplam';
$lang['merge_field_note'] = 'Birleştirme alanları eklemek için \'@\' işaretini kullanın.';
$lang['send_to_all'] = 'Herkese Gönder';
$lang['or'] = 'VEYA';

$lang['convert_whatsapp_message_to_lead'] = 'Yeni Potansiyel Müşteri Otomatik Olarak Kazan (yeni whatsapp mesajlarını potansiyel müşteri olarak dönüştür)';
$lang['leads_status'] = 'Potansiyel Müşteri Durumu';
$lang['leads_assigned'] = 'Potansiyel Müşteri Atandı';
$lang['whatsapp_auto_lead'] = 'Whatsapp Otomatik Potansiyel Müşteri';
$lang['webhooks_label'] = 'Whatsapp alınan verileri yeniden gönderecek';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'WebHooks Yeniden Gönderimini Etkinleştir';
$lang['chat'] = 'Sohbet';
$lang['black_listed_phone_numbers'] = 'Siyah listeye alınmış telefon numaraları';
$lang['sent_status'] = 'Gönderim Durumu';

$lang['active'] = 'Aktif';
$lang['approved'] = 'Onaylı';
$lang['this_month'] = 'bu ay';
$lang['open_chats'] = 'Açık Sohbetler';
$lang['resolved_conversations'] = 'Çözülen Sohbetler';
$lang['messages_sent'] = 'Gönderilen Mesajlar';
$lang['account_connected'] = 'Hesap bağlandı';
$lang['account_disconnected'] = 'Hesap bağlantısı kesildi';
$lang['webhook_verify_token'] = 'Webhook doğrulama jetonu';
// Chat integration
$lang['chat_message_note'] = 'Mesaj kısa sürede gönderilecektir. Lütfen yeni bir iletişimse, iletişim kurana kadar bu listede görünmeyeceğini unutmayın!';

$lang['activity_log'] = 'Etkinlik Günlüğü';
$lang['whatsapp_logs'] = 'Whatsapp Kayıtları';
$lang['response_code'] = 'Yanıt Kodu';
$lang['recorded_on'] = 'Kaydedildiği Tarih';

$lang['request_details'] = 'İstek Detayları';
$lang['raw_content'] = 'Ham İçerik';
$lang['headers'] = 'Başlıklar';
$lang['format_type'] = 'Format Türü';

// Permission section
$lang['show_campaign'] = 'Kampanyayı Göster';
$lang['clear_log'] = 'Günlüğü Temizle';
$lang['log_activity'] = 'Etkinlik Günlüğü';
$lang['load_template'] = 'Şablonu Yükle';

$lang['action'] = 'Eylem';
$lang['total_parameters'] = 'Toplam Parametreler';
$lang['template_name'] = 'Şablon Adı';
$lang['log_cleared_successfully'] = 'Günlük başarıyla temizlendi';
$lang['whatsbot_stats'] = 'WhatsBot İstatistikleri';

$lang['not_found_or_deleted'] = 'Bulunamadı veya silindi';
$lang['response'] = 'Yanıt';

$lang['select_image'] = 'Görüntü Seç';
$lang['image'] = 'Görüntü';
$lang['image_deleted_successfully'] = 'Görüntü başarıyla silindi';
$lang['whatsbot_settings'] = 'Whatsbot Ayarları';
$lang['maximum_file_size_should_be'] = 'Maksimum dosya boyutu olmalıdır ';
$lang['allowed_file_types'] = 'İzin verilen dosya türleri: ';

$lang['send_image'] = 'Görüntü Gönder';
$lang['send_video'] = 'Video Gönder';
$lang['send_document'] = 'Belge Gönder';
$lang['record_audio'] = 'Ses Kaydet';
$lang['chat_media_info'] = 'Desteklenen İçerik Türleri ve Medya Boyutu için Daha Fazla Bilgi';
$lang['help'] = 'Yardım';

// v1.1.0
$lang['clone'] = 'Klonla';
$lang['bot_clone_successfully'] = 'Bot başarıyla klonlandı';
$lang['all_chat'] = 'Tüm Sohbetler';
$lang['from'] = 'Kimden:';
$lang['phone_no'] = 'Telefon No:';
$lang['supportagent'] = 'Destek Temsilcisi';
$lang['assign_chat_permission_to_support_agent'] = 'Sadece destek temsilcisine sohbet izni ver';
$lang['enable_whatsapp_notification_sound'] = 'Whatsapp sohbet bildirim sesi etkinleştir';
$lang['notification_sound'] = 'Bildirim Sesi';
$lang['trigger_keyword'] = 'Tetikleyici Anahtar Kelime';
$lang['modal_title'] = 'Destek Temsilcisi Seç';
$lang['close_btn'] = 'Kapat';
$lang['save_btn'] = 'Kaydet';
$lang['support_agent'] = 'Destek Temsilcisi';
$lang['change_support_agent'] = 'Destek Temsilcisini Değiştir';
$lang['replay_message'] = 'Mesajı gönderemezsiniz, 24 saat doldu.';
$lang['support_agent_note'] = '<strong>Not: </strong>Destek temsilcisi özelliğini etkinleştirdiğinizde, potansiyel müşteri atanmış kişi otomatik olarak sohbete atanacaktır. Yöneticiler, sohbet sayfasından yeni bir temsilci de atayabilir.';
$lang['permission_bot_clone'] = 'Botu Klonla';
$lang['remove_chat'] = 'Sohbeti Kaldır';
$lang['default_message_on_no_match'] = 'Varsayılan Yanıt - herhangi bir anahtar kelime eşleşmediğinde';
$lang['default_message_note'] = '<strong>Not: </strong>Bu seçeneği etkinleştirmek, webhook yükünüzü artıracaktır. Daha fazla bilgi için bu <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">bağlantıya</a> göz atın.';

$lang['whatsbot_connect_account'] = 'Whatsbot Hesabı Bağla';
$lang['whatsbot_message_bot'] = 'Whatsbot Mesaj Botu';
$lang['whatsbot_template_bot'] = 'Whatsbot Şablon Botu';
$lang['whatsbot_template'] = 'Whatsbot Şablonu';
$lang['whatsbot_campaigns'] = 'Whatsbot Kampanyaları';
$lang['whatsbot_chat'] = 'Whatsbot Sohbet';
$lang['whatsbot_log_activity'] = 'Whatsbot Aktivite Günlüğü';
$lang['message_templates_not_exists_note'] = 'Meta şablon izni eksik. Lütfen bunu Meta hesabınızda etkinleştirin';

// v1.2.0
$lang['ai_prompt'] = 'AI İstekleri';
$lang['ai_prompt_note'] = 'AI istekleri için, bu özelliği etkinleştirmek üzere bir mesaj girin veya zaten etkinleştirilmiş AI isteklerini kullanın';
$lang['emojis'] = 'Emojiler';
$lang['translate'] = 'Çevir';
$lang['change_tone'] = 'Tonu Değiştir';
$lang['professional'] = 'Profesyonel';
$lang['friendly'] = 'Dostça';
$lang['empathetic'] = 'Empatik';
$lang['straightforward'] = 'Açık';
$lang['simplify_language'] = 'Dili Basitleştir';
$lang['fix_spelling_and_grammar'] = 'Yazım ve Dilbilgisi Düzelt';

// AI Entegrasyonu
$lang['ai_integration'] = 'AI Entegrasyonu';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'OpenAI Gizli Anahtarı - <a href="https://platform.openai.com/account/api-keys" target="_blank">Gizli anahtarı nerede bulabilirsiniz?</a>';
$lang['chat_text_limit'] = 'Sohbet Metin Limiti';
$lang['chat_text_limit_note'] = 'Operasyonel maliyetleri optimize etmek için, OpenAI\'nin sohbet yanıtlarının kelime sayısını sınırlamayı düşünün';
$lang['chat_model'] = 'Sohbet Modeli';
$lang['openai_organizations'] = 'OpenAi Organizasyonları';
$lang['template_type'] = 'Şablon Türü';
$lang['update'] = 'Güncelle';
$lang['open_ai_key_verification_fail'] = 'OpenAi Anahtar Doğrulaması Ayarlardan Bekleniyor, lütfen OpenAI hesabınızı bağlayın';
$lang['enable_wb_openai'] = 'Sohbette OpenAI\'yi Etkinleştir';
$lang['webhook_resend_method'] = 'Webhook Yeniden Gönderim Yöntemi';
$lang['search_language'] = 'Dil ara...';
$lang['document'] = 'Belge';
$lang['select_document'] = 'Belge Seç';
$lang['attchment_deleted_successfully'] = 'Eki Başarıyla Sildi';
$lang['attach_image_video_docs'] = 'Görüntü, Video, Belgeleri Ekleyin';
$lang['choose_file_type'] = 'Dosya Türünü Seç';
$lang['max_size'] = 'Maks Boyut: ';

// v1.3.0

// CSV içe aktarma
$lang['bulk_campaigns'] = 'Toplu Kampanyalar';
$lang['upload_csv'] = 'CSV Yükle';
$lang['upload'] = 'Yükle';
$lang['csv_uploaded_successfully'] = 'CSV Dosyası Başarıyla Yüklendi';
$lang['please_select_file'] = 'Lütfen CSV Dosyası Seçin';
$lang['phonenumber_field_is_required'] = 'Telefon numarası alanı gereklidir';
$lang['out_of_the'] = 'içinde';
$lang['records_in_your_csv_file'] = 'CSV dosyanızdaki kayıtlar,';
$lang['valid_the_campaign_can_be_sent'] = 'geçerli kayıtlar.<br /> Kampanya bu kayıtlara başarıyla gönderilebilir';
$lang['users'] = 'kullanıcılar';
$lang['campaigns_from_csv_file'] = 'CSV Dosyasından Kampanyalar';
$lang['download_sample'] = 'Örnek İndir';
$lang['csv_rule_1'] = '1. <b>Telefon Numarası Sütun Gereksinimi:</b> CSV dosyanızda "Phoneno" adında bir sütun bulunmalıdır. Bu sütundaki her kayıtta geçerli bir iletişim numarası olmalı, ülke kodu ile birlikte doğru formatta yazılmalıdır. <br /><br />';
$lang['csv_rule_2'] = '2. <b>CSV Formatı ve Kodlaması:</b> CSV verileriniz belirtilen formatı izlemelidir. CSV dosyanızın ilk satırı, örnek tabloda gösterildiği gibi sütun başlıklarını içermelidir. Dosyanızın UTF-8 kodlamasında olduğundan emin olun, böylece kodlama sorunları yaşanmaz.';
$lang['please_upload_valid_csv_file'] = 'Lütfen geçerli CSV dosyası yükleyin';
$lang['please_add_valid_number_in_csv_file'] = 'Lütfen CSV dosyasına geçerli <b>Phoneno</b> ekleyin';
$lang['total_send_campaign_list'] = 'Toplam gönderilen kampanya: %s';
$lang['sample_data'] = 'Örnek Veri';
$lang['firstname'] = 'İsim';
$lang['lastname'] = 'Soyisim';
$lang['phoneno'] = 'Telefon Numarası';
$lang['email'] = 'E-posta';
$lang['country'] = 'Ülke';
$lang['download_sample_and_read_rules'] = 'Örnek Dosyayı İndirin & Kuralları Okuyun';
$lang['please_wait_your_request_in_process'] = 'Lütfen bekleyin, isteğiniz işleniyor.';
$lang['whatsbot_bulk_campaign'] = 'Whatsbot Toplu Kampanyalar';
$lang['csv_campaign'] = 'CSV Kampanyası';

// Hazır yanıt
$lang['canned_reply'] = 'Hazır Yanıt';
$lang['canned_reply_menu'] = 'Hazır Yanıt';
$lang['create_canned_reply'] = 'Hazır Yanıt Oluştur';
$lang['title'] = 'Başlık';
$lang['desc'] = 'Açıklama';
$lang['public'] = 'Herkese Açık';
$lang['action'] = 'Eylem';
$lang['delete_successfully'] = 'Yanıt silindi.';
$lang['cannot_delete'] = 'Yanıt silinemedi.';
$lang['whatsbot_canned_reply'] = 'Whatsbot Hazır Yanıt';
$lang['reply'] = 'Yanıt';

// AI İstekleri
$lang['ai_prompts'] = 'AI İstekleri';
$lang['create_ai_prompts'] = 'AI İstekleri Oluştur';
$lang['name'] = 'İsim';
$lang['action'] = 'Eylem';
$lang['prompt_name'] = 'İstek adı';
$lang['prompt_action'] = 'İstek eylemi';
$lang['whatsbot_ai_prompts'] = 'Whatsbot AI İstekleri';

// yeni sohbet
$lang['replying_to'] = 'Cevaplıyorum :';
$lang['download_document'] = 'Belgeyi İndir';
$lang['custom_prompt'] = 'Özel İstek';
$lang['canned_replies'] = 'Hazır Yanıtlar';
$lang['use_@_to_add_merge_fields'] = '\'@\' kullanarak birleştirme alanları ekleyin';
$lang['type_your_message'] = 'Mesajınızı yazın';
$lang['you_cannot_send_a_message_using_this_number'] = 'Bu numara ile mesaj gönderemezsiniz.';

// bot akışı
$lang['bot_flow'] = 'Bot Akışı';
$lang['create_new_flow'] = 'Yeni Akış Oluştur';
$lang['flow_name'] = 'Akış Adı';
$lang['flow'] = 'Akış';
$lang['bot_flow_builder'] = 'Bot Akış Yapıcı';
$lang['you_can_not_upload_file_type'] = 'Şu <b> %s </b> türde dosyayı yükleyemezsiniz';
$lang['whatsbot_bot_flow'] = 'Whatsbot Bot Akışı';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Sohbet Geçmişini Otomatik Temizle';
$lang['enable_auto_clear_chat_history'] = 'Sohbet Geçmişini Otomatik Temizlemeyi Etkinleştir';
$lang['auto_clear_time'] = 'Otomatik Temizleme Süresi';
$lang['clear_chat_history_note'] = '<strong>Not: </strong> Eğer otomatik temizleme özelliğini etkinleştirirseniz, belirttiğiniz gün sayısına göre sohbet geçmişi otomatik olarak temizlenecektir, cron işi çalıştığında.';
$lang['source'] = 'Kaynak';
$lang['groups'] = 'Gruplar';

// v1.3.3
$lang['click_user_to_chat'] = 'Kullanıcıya tıklayarak sohbet edin';
$lang['searching'] = 'Aranıyor...';
$lang['filters'] = 'Filtreler';
$lang['relation_type'] = 'İlişki Türü';
$lang['groups'] = 'Gruplar';
$lang['source'] = 'Kaynak';
$lang['status'] = 'Durum';
$lang['select_type'] = 'Tür Seç';
$lang['select_agents'] = 'Ajanları Seç';
$lang['select_group'] = 'Grubu Seç';
$lang['select_source'] = 'Kaynağı Seç';
$lang['select_status'] = 'Durumu Seç';
$lang['agents'] = 'Ajanlar';

// v1.4.2
$lang['read_only'] = 'Salt okunur';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';

$lang['send_location'] = 'Send Location';
$lang['search_for_a_place'] = 'Search for a place';
$lang['address_optional'] = 'Address (optional)';
$lang['latitude'] = 'Latitude';
$lang['longitude'] = 'Longitude';
$lang['drag_the_maker_to_set_the_location'] = 'Drag the marker to set the location or search for a place.';
$lang['use_my_location'] = 'Use My Current Location';
$lang['send_location'] = 'Send Location';

$lang['initiate_chat'] = 'Initiate Chat';
$lang['please_select_at_least_one_lead'] = 'Please select at least one lead';
$lang['chat_initiated_successfully'] = 'Chat Initiated  successfuly';
$lang['something_went_wrong'] = "Something Went Wrong";

$lang['video'] = "Video";
$lang['select_video'] = "Select Video";

$lang['whatsbot_cron'] = 'Whatsbot Cron';

// Session Management
$lang['session_management']                 = 'WhatsApp Session Management';
$lang['enable_session_management']          = 'Enable session management';
$lang['session_management_help_text']       = 'WhatsApp Cloud API has a 24-hour session limit for business-initiated messages. Enable this to send a reminder before the session expires.';
$lang['session_expiry_message']             = 'Session expiry message';
$lang['session_expiry_message_help']        = 'This message will be sent to customers before their 24-hour session expires.';
$lang['session_expiry_hours']               = 'Hours before expiry to send reminder';
$lang['session_expiry_hours_help']          = 'Set how many hours after the last customer message to send the reminder (max 23 hours).';
$lang['include_session_reset_button']       = 'Include quick reply button';
$lang['include_session_reset_button_help']  = 'Add a quick reply button to make it easier for customers to respond.';
$lang['session_management_note']            = 'Note: This feature requires the cron job to be properly configured. The cron job should run at least once per hour.';
$lang['session_reset_button_text']          = 'Continue Conversation';
$lang['whatsapp_session_management']        = 'WhatsApp Session Management';
$lang['active_sessions']                    = 'Active Sessions';
$lang['expiring_sessions']                  = 'Expiring Sessions';
$lang['sessions_reset_today']               = 'Sessions Reset Today';
$lang['sessions_reset_week']                = 'Sessions Reset (7 Days)';
$lang['session_management_disabled']        = 'WhatsApp Session Management is currently disabled.';

// Dashboard and Reports
$lang['session_reset_count']                = 'Session Reset Count';
$lang['session_expired']                    = 'Session Expired';
$lang['session_active']                     = 'Session Active';
$lang['session_reset_success_rate']         = 'Reset Success Rate';
$lang['customer_responded']                 = 'Customer Responded';
$lang['customer_not_responded']             = 'Customer Did Not Respond';
$lang['enable_now']                         = 'Enable Now';
