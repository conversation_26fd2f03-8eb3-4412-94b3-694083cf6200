<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Anslut Konto';
$lang['connect_whatsapp_business'] = 'Anslut WhatsApp Business';
$lang['campaigning'] = 'Kampanjarbete';
$lang['business_account_id_description'] = 'Ditt WhatsApp Business-konto (WABA) ID';
$lang['access_token_description'] = 'Ditt användartoken efter att du registrerat ett konto på Facebook Developers Portal';
$lang['whatsapp_business_account_id'] = 'WhatsApp Business-konto ID';
$lang['whatsapp_access_token'] = 'WhatsApp Åtkomsttoken';
$lang['webhook_callback_url'] = 'Webhook Callback URL';
$lang['verify_token'] = 'Verifiera Token';
$lang['connect'] = 'Anslut';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'Anslut Konto med Ett Klick';
$lang['connect_your_whatsapp_account'] = 'Anslut Ditt WhatsApp-konto';
$lang['copy'] = 'Kopiera';
$lang['copied'] = 'Kopierat!!';
$lang['disconnect'] = 'Koppla bort';
$lang['number'] = 'Nummer';
$lang['number_id'] = 'Nummer ID';
$lang['quality'] = 'Kvalitet';
$lang['status'] = 'Status';
$lang['business_account_id'] = 'Business-konto ID';
$lang['permissions'] = 'Behörigheter';
$lang['phone_number_id_description'] = 'ID för telefonnumret anslutet till WhatsApp Business API. Om du är osäker kan du använda ett GET-förfrågan om telefonnummer-ID för att hämta det från WhatsApp API (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'Nummer ID för det WhatsApp-registrerade telefonnumret';
$lang['update_details'] = 'Uppdatera Detaljer';

$lang['bots'] = 'Bots';
$lang['bots_management'] = 'Bots Hantering';
$lang['create_template_base_bot'] = 'Skapa mallbaserad bot';
$lang['create_message_bot'] = 'Skapa meddelandebot';
$lang['type'] = 'Typ';
$lang['message_bot'] = 'Meddelandebot';
$lang['new_template_bot'] = 'Ny Mallbot';
$lang['new_message_bot'] = 'Ny Meddelandebot';
$lang['bot_name'] = 'Botnamn';
$lang['reply_text'] = 'Svarstext <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Texten som skickas till leadet eller kontakten. Du kan också använda {companyname}, {crm_url} eller andra anpassade sammanslagningsfält för lead eller kontakt, eller använd \'@\' för att hitta tillgängliga sammanslagningsfält" data-placement="bottom"></i> <span class="text-muted">(Maximalt tillåtna tecken är 1024)</span>';
$lang['reply_type'] = 'Svarsformat';
$lang['trigger'] = 'Trigger';
$lang['header'] = 'Rubrik';
$lang['footer_bot'] = 'Fotnot <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximalt tillåtna tecken är 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Alternativ 1: Bot med svarsknappar';
$lang['bot_with_button_link'] = 'Alternativ 2: Bot med knapp-länk - CTA URL';
$lang['button1'] = 'Knapp1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximalt tillåtna tecken är 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'Knapp1 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximalt tillåtna tecken är 256" data-placement="bottom"></i>';
$lang['button2'] = 'Knapp2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximalt tillåtna tecken är 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'Knapp2 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximalt tillåtna tecken är 256" data-placement="bottom"></i>';
$lang['button3'] = 'Knapp3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximalt tillåtna tecken är 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'Knapp3 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximalt tillåtna tecken är 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Knappnamn <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximalt tillåtna tecken är 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Knapp Länk';
$lang['enter_name'] = 'Ange Namn';
$lang['select_reply_type'] = 'Välj svarsformat';
$lang['enter_bot_reply_trigger'] = 'Ange botens svarstrigger';
$lang['enter_header'] = 'Ange rubrik';
$lang['enter_footer'] = 'Ange fotnot';
$lang['enter_button1'] = 'Ange knapp1';
$lang['enter_button1_id'] = 'Ange knapp1 ID';
$lang['enter_button2'] = 'Ange knapp2';
$lang['enter_button2_id'] = 'Ange knapp2 ID';
$lang['enter_button3'] = 'Ange knapp3';
$lang['enter_button3_id'] = 'Ange knapp3 ID';
$lang['enter_button_name'] = 'Ange knappnamn';
$lang['enter_button_url'] = 'Ange knapp URL';
$lang['on_exact_match'] = 'Svarsbot: Vid exakt matchning';
$lang['when_message_contains'] = 'Svarsbot: När meddelandet innehåller';
$lang['when_client_send_the_first_message'] = 'Välkomstsvar - när lead eller klient skickar första meddelandet';
$lang['bot_create_successfully'] = 'Bot skapades framgångsrikt';
$lang['bot_update_successfully'] = 'Bot uppdaterades framgångsrikt';
$lang['bot_deleted_successfully'] = 'Bot raderades framgångsrikt';
$lang['templates'] = 'Mallar';
$lang['template_data_loaded'] = 'Mallarna laddades framgångsrikt';
$lang['load_templates'] = 'Ladda Mallar';
$lang['template_management'] = 'Mallhantering';


// campaigns
$lang['campaign'] = 'Kampanj';
$lang['campaigns'] = 'Kampanjer';
$lang['send_new_campaign'] = 'Skicka ny kampanj';
$lang['campaign_name'] = 'Kampanjnamn';
$lang['template'] = 'Mall';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Per klient, baserat på kontaktens tidszon" data-placement="left"></i>Planerad skickningstid';
$lang['scheduled_time_description'] = 'Per klient, baserat på kontaktens tidszon';
$lang['ignore_scheduled_time_and_send_now'] = 'Ignorera planerad tid och skicka nu';
$lang['template'] = 'Mall';
$lang['leads'] = 'Leads';
$lang['delivered_to'] = 'Levererad till';
$lang['read_by'] = 'Läst av';
$lang['variables'] = 'Variabler';
$lang['body'] = 'Kropp';
$lang['variable'] = 'Variabel';
$lang['match_with_selected_field'] = 'Matcha med valt fält';
$lang['preview'] = 'Förhandsgranskning';
$lang['send_campaign'] = 'Skicka kampanj';
$lang['send_to'] = 'Skicka till';
$lang['send_campaign'] = 'Skicka kampanj';
$lang['view_campaign'] = 'Visa kampanj';
$lang['campaign_daily_task'] = 'Kampanjens dagliga uppgift';
$lang['back'] = 'Tillbaka';
$lang['phone'] = 'Telefon';
$lang['message'] = 'Meddelande';
$lang['currently_type_not_supported'] = 'För närvarande stöds inte typ <strong> %s </strong> av mall!';
$lang['of_your'] = 'av dina ';
$lang['contacts'] = 'Kontakter';
$lang['select_all_leads'] = 'Välj alla leads';
$lang['select_all_note_leads'] = 'Om du väljer detta inkluderas alla framtida leads i denna kampanj.';
$lang['select_all_note_contacts'] = 'Om du väljer detta inkluderas alla framtida kontakter i denna kampanj.';

$lang['verified_name'] = 'Verifierat namn';
$lang['mark_as_default'] = 'Markera som standard';
$lang['default_number_updated'] = 'Standardtelefonnummer ID uppdaterat framgångsrikt';
$lang['currently_using_this_number'] = 'Använder för närvarande detta nummer';
$lang['leads'] = 'Leads';
$lang['pause_campaign'] = 'Pausa kampanj';
$lang['resume_campaign'] = 'Återuppta kampanj';
$lang['campaign_resumed'] = 'Kampanj återupptagen';
$lang['campaign_paused'] = 'Kampanj pausad';

//Template
$lang['body_data'] = 'Kroppens data';
$lang['category'] = 'Kategori';

// Template bot
$lang['create_new_template_bot'] = 'Skapa ny mall-bot';
$lang['template_bot'] = 'Mall-bot';
$lang['variables'] = 'Variabler';
$lang['preview'] = 'Förhandsgranskning';
$lang['template'] = 'Mall';
$lang['bot_content_1'] = 'Detta meddelande kommer att skickas till kontakten när triggerregeln uppfylls i meddelandet som skickas av kontakten.';
$lang['save_bot'] = 'Spara bot';
$lang['please_select_template'] = 'Vänligen välj en mall';
$lang['use_manually_define_value'] = 'Använd manuellt definierat värde';
$lang['merge_fields'] = 'Sammanfoga fält';
$lang['template_bot_create_successfully'] = 'Mall-bot skapad framgångsrikt';
$lang['template_bot_update_successfully'] = 'Mall-bot uppdaterad framgångsrikt';
$lang['text_bot'] = 'Textbot';
$lang['option_2_bot_with_link'] = 'Alternativ 2: Bot med knapplänk - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Alternativ 3: Bot med fil';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Meddelande skickas när fördröjning i svar förväntas';
$lang['bot_delay_response_placeholder'] = 'Ge mig ett ögonblick, jag har svaret snart';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Relationstyp';
$lang['select_all'] = 'Välj alla';
$lang['total'] = 'Totalt';
$lang['merge_field_note'] = 'Använd \'@\'-tecknet för att lägga till sammanfogade fält.';
$lang['send_to_all'] = 'Skicka till alla ';
$lang['or'] = 'ELLER';

$lang['convert_whatsapp_message_to_lead'] = 'Förvärva ny lead automatiskt (konvertera nya whatsapp-meddelanden till lead)';
$lang['leads_status'] = 'Leads status';
$lang['leads_assigned'] = 'Lead tilldelad';
$lang['whatsapp_auto_lead'] = 'Whatsapp Auto Lead';
$lang['webhooks_label'] = 'Whatsapp mottagna data kommer att skickas igen till';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Aktivera WebHooks återställning';
$lang['chat'] = 'Chat';
$lang['black_listed_phone_numbers'] = 'Svartlistade telefonnummer';
$lang['sent_status'] = 'Skickad status';

$lang['active'] = 'Aktiv';
$lang['approved'] = 'Godkänd';
$lang['this_month'] = 'denna månad';
$lang['open_chats'] = 'Öppna chattar';
$lang['resolved_conversations'] = 'Lösta konversationer';
$lang['messages_sent'] = 'Skickade meddelanden';
$lang['account_connected'] = 'Konto anslutet';
$lang['account_disconnected'] = 'Konto frånkopplat';
$lang['webhook_verify_token'] = 'Webhook verifiera token';
// Chat integration
$lang['chat_message_note'] = 'Meddelandet kommer att skickas snart. Observera att om det är en ny kontakt, kommer den inte att visas i denna lista förrän kontakten börjar interagera med dig!';

$lang['activity_log'] = 'Aktivitetslogg';
$lang['whatsapp_logs'] = 'Whatsapp-loggar';
$lang['response_code'] = 'Responskod';
$lang['recorded_on'] = 'Registrerad den';

$lang['request_details'] = 'Begärningsdetaljer';
$lang['raw_content'] = 'Råinnehåll';
$lang['headers'] = 'Rubriker';
$lang['format_type'] = 'Formattyp';

// Permission section
$lang['show_campaign'] = 'Visa kampanj';
$lang['clear_log'] = 'Rensa logg';
$lang['log_activity'] = 'Logga aktivitet';
$lang['load_template'] = 'Ladda mall';

$lang['action'] = 'Åtgärd';
$lang['total_parameters'] = 'Totalt antal parametrar';
$lang['template_name'] = 'Mallnamn';
$lang['log_cleared_successfully'] = 'Logg rensad framgångsrikt';
$lang['whatsbot_stats'] = 'WhatsBot-statistik';

$lang['not_found_or_deleted'] = 'Inte hittad eller raderad';
$lang['response'] = 'Svar';

$lang['select_image'] = 'Välj bild';
$lang['image'] = 'Bild';
$lang['image_deleted_successfully'] = 'Bild raderad framgångsrikt';
$lang['whatsbot_settings'] = 'Whatsbot-inställningar';
$lang['maximum_file_size_should_be'] = 'Maximal filstorlek bör vara ';
$lang['allowed_file_types'] = 'Tillåtna filtyper: ';

$lang['send_image'] = 'Skicka bild';
$lang['send_video'] = 'Skicka video';
$lang['send_document'] = 'Skicka dokument';
$lang['record_audio'] = 'Spela in ljud';
$lang['chat_media_info'] = 'Mer information om stödda innehållstyper och efterbearbetningsfilstorlek';
$lang['help'] = 'Hjälp';

// v1.1.0
$lang['clone'] = 'Klona';
$lang['bot_clone_successfully'] = 'Bot klonad framgångsrikt';
$lang['all_chat'] = 'Alla chattar';
$lang['from'] = 'Från:';
$lang['phone_no'] = 'Telefonnr:';
$lang['supportagent'] = 'Supportagent';
$lang['assign_chat_permission_to_support_agent'] = 'Tilldela chattbehörighet till supportagent endast';
$lang['enable_whatsapp_notification_sound'] = 'Aktivera ljud för WhatsApp-notifikationer';
$lang['notification_sound'] = 'Notifikationsljud';
$lang['trigger_keyword'] = 'Triggernyckelord';
$lang['modal_title'] = 'Välj supportagent';
$lang['close_btn'] = 'Stäng';
$lang['save_btn'] = 'Spara';
$lang['support_agent'] = 'Supportagent';
$lang['change_support_agent'] = 'Byt supportagent';
$lang['replay_message'] = 'Du kan inte skicka meddelande 24 timmar har passerat.';
$lang['support_agent_note'] = '<strong>Notera: </strong>När du aktiverar supportagent-funktionen kommer lead-tilldelaren automatiskt att tilldelas chatten. Administratörer kan också tilldela en ny agent från chatt-sidan.';
$lang['permission_bot_clone'] = 'Klona bot';
$lang['remove_chat'] = 'Ta bort chatt';
$lang['default_message_on_no_match'] = 'Standardmeddelande - om något nyckelord inte matchar';
$lang['default_message_note'] = '<strong>Notera: </strong>Aktivering av denna funktion ökar din webhook-belastning. För mer information, besök denna <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">länk</a>.';
$lang['whatsbot_connect_account'] = 'Whatsbot Anslut Konto';
$lang['whatsbot_message_bot'] = 'Whatsbot Meddelande Bot';
$lang['whatsbot_template_bot'] = 'Whatsbot Mall Bot';
$lang['whatsbot_template'] = 'Whatsbot Mall';
$lang['whatsbot_campaigns'] = 'Whatsbot Kampanjer';
$lang['whatsbot_chat'] = 'Whatsbot Chatt';
$lang['whatsbot_log_activity'] = 'Whatsbot Logg Aktivitet';
$lang['message_templates_not_exists_note'] = 'Meta mallbehörighet saknas. Vänligen aktivera det i ditt Meta-konto';

// v1.2.0
$lang['ai_prompt'] = 'AI Frågor';
$lang['ai_prompt_note'] = 'För AI-frågor, vänligen skriv ett meddelande för att aktivera funktionen, eller använd AI-frågor om det redan är aktiverat';
$lang['emojis'] = 'Emojis';
$lang['translate'] = 'Översätta';
$lang['change_tone'] = 'Ändra Ton';
$lang['professional'] = 'Professionell';
$lang['friendly'] = 'Vänlig';
$lang['empathetic'] = 'Empatisk';
$lang['straightforward'] = 'Rakt på sak';
$lang['simplify_language'] = 'Förenkla Språk';
$lang['fix_spelling_and_grammar'] = 'Rätta Stavning & Grammatik';

$lang['ai_integration'] = 'AI Integration';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'OpenAI Hemlig Nyckel - <a href="https://platform.openai.com/account/api-keys" target="_blank">Var kan du hitta hemlig nyckel?</a>';
$lang['chat_text_limit'] = 'Chat Textgräns';
$lang['chat_text_limit_note'] = 'För att optimera driftskostnader, överväg att begränsa ordantalet i OpenAI:s chatt svar';
$lang['chat_model'] = 'Chatt Modell';
$lang['openai_organizations'] = 'OpenAi Organisationer';
$lang['template_type'] = 'Malltyp';
$lang['update'] = 'Uppdatera';
$lang['open_ai_key_verification_fail'] = 'Öppen AI Nyckel Verifiering väntar på inställningar, vänligen anslut ditt OpenAI-konto';
$lang['enable_wb_openai'] = 'Aktivera OpenAI i chatt';
$lang['webhook_resend_method'] = 'Webhook Återsändningsmetod';
$lang['search_language'] = 'Sök språk...';
$lang['document'] = 'Dokument';
$lang['select_document'] = 'Välj Dokument';
$lang['attchment_deleted_successfully'] = 'Bilaga raderad framgångsrikt';
$lang['attach_image_video_docs'] = 'Bifoga Bild Video Dokument';
$lang['choose_file_type'] = 'Välj Filtyp';
$lang['max_size'] = 'Max Storlek: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Bulk Kampanjer';
$lang['upload_csv'] = 'Ladda upp CSV';
$lang['upload'] = 'Ladda upp';
$lang['csv_uploaded_successfully'] = 'CSV-fil uppladdad framgångsrikt';
$lang['please_select_file'] = 'Vänligen välj CSV-fil';
$lang['phonenumber_field_is_required'] = 'Telefonnummerfältet är obligatoriskt';
$lang['out_of_the'] = 'Av de';
$lang['records_in_your_csv_file'] = 'poster i din CSV-fil,';
$lang['valid_the_campaign_can_be_sent'] = 'poster är giltiga.<br /> Kampanjen kan skickas framgångsrikt till dessa';
$lang['users'] = 'användare';
$lang['campaigns_from_csv_file'] = 'Kampanjer från CSV-fil';
$lang['download_sample'] = 'Ladda ner Exempel';
$lang['csv_rule_1'] = '1. <b>Telefonnummer Kolumn Krav:</b> Din CSV-fil måste innehålla en kolumn med namnet "Phoneno." Varje post i denna kolumn ska innehålla ett giltigt kontakt nummer, korrekt formaterat med landskoden, inklusive "+" tecknet. <br /><br />';
$lang['csv_rule_2'] = '2. <b>CSV-format och Kodning:</b> Dina CSV-data bör följa det angivna formatet. Den första raden av din CSV-fil måste innehålla kolumnrubriker, som visas i exempel tabellen. Se till att din fil är kodad i UTF-8 för att förhindra eventuella kodningsproblem.';
$lang['please_upload_valid_csv_file'] = 'Vänligen ladda upp en giltig CSV-fil';
$lang['please_add_valid_number_in_csv_file'] = 'Vänligen lägg till giltigt <b>Phoneno</b> i CSV-fil';
$lang['total_send_campaign_list'] = 'Totalt skickade kampanjer: %s';
$lang['sample_data'] = 'Exempeldata';
$lang['firstname'] = 'Förnamn';
$lang['lastname'] = 'Efternamn';
$lang['phoneno'] = 'Phoneno';
$lang['email'] = 'E-post';
$lang['country'] = 'Land';
$lang['download_sample_and_read_rules'] = 'Ladda ner Exempelfil & Läs Regler';
$lang['please_wait_your_request_in_process'] = 'Vänligen vänta, din begäran behandlas för närvarande.';
$lang['whatsbot_bulk_campaign'] = 'Whatsbot Bulk Kampanjer';
$lang['csv_campaign'] = 'CSV Kampanj';

// Canned reply
$lang['canned_reply'] = 'Färdig Svar';
$lang['canned_reply_menu'] = 'Färdig Svar';
$lang['create_canned_reply'] = 'Skapa Färdig Svar';
$lang['title'] = 'Titel';
$lang['desc'] = 'Beskrivning';
$lang['public'] = 'Offentlig';
$lang['action'] = 'Åtgärd';
$lang['delete_successfully'] = 'Svar raderat.';
$lang['cannot_delete'] = 'Svar kan inte raderas.';
$lang['whatsbot_canned_reply'] = 'Whatsbot Färdig Svar';
$lang['reply'] = 'Svar';

//AI Prompts
$lang['ai_prompts'] = 'AI Frågor';
$lang['create_ai_prompts'] = 'Skapa AI Frågor';
$lang['name'] = 'Namn';
$lang['action'] = 'Åtgärd';
$lang['prompt_name'] = 'Fråge namn';
$lang['prompt_action'] = 'Fråge åtgärd';
$lang['whatsbot_ai_prompts'] = 'Whatsbot AI Frågor';

// new chat
$lang['replying_to'] = 'Svara på:';
$lang['download_document'] = 'Ladda ner Dokument';
$lang['custom_prompt'] = 'Anpassad Fråga';
$lang['canned_replies'] = 'Färdiga Svar';
$lang['use_@_to_add_merge_fields'] = 'Använd \'@\' för att lägga till sammanfogningsfält';
$lang['type_your_message'] = 'Skriv ditt meddelande';
$lang['you_cannot_send_a_message_using_this_number'] = 'Du kan inte skicka ett meddelande med detta nummer.';

// bot flow
$lang['bot_flow'] = 'Bot Flöde';
$lang['create_new_flow'] = 'Skapa Nytt Flöde';
$lang['flow_name'] = 'Flödesnamn';
$lang['flow'] = 'Flöde';
$lang['bot_flow_builder'] = 'Bot Flöde Byggare';
$lang['you_can_not_upload_file_type'] = 'Du kan inte ladda upp <b> %s </b> typ av fil';
$lang['whatsbot_bot_flow'] = 'Whatsbot Bot Flöde';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Automatisk Rensa Chatt Historik';
$lang['enable_auto_clear_chat_history'] = 'Aktivera Automatisk Rensning av Chatt Historik';
$lang['auto_clear_time'] = 'Automatisk Rensning Tid';
$lang['clear_chat_history_note'] = '<strong>Obs: </strong> Om du aktiverar funktionen för automatisk rensning av chatt historik, kommer den automatiskt att rensa chatt historiken baserat på det antal dagar du specificerar, när cron-jobbet körs.';
$lang['source'] = 'Källa';
$lang['groups'] = 'Grupper';


// v1.3.3
$lang['click_user_to_chat'] = 'Klicka på användaren för att chatta';
$lang['searching'] = 'Söker...';
$lang['filters'] = 'Filter';
$lang['relation_type'] = 'Relationstyp';
$lang['groups'] = 'Grupper';
$lang['source'] = 'Källa';
$lang['status'] = 'Status';
$lang['select_type'] = 'Välj typ';
$lang['select_agents'] = 'Välj agenter';
$lang['select_group'] = 'Välj grupp';
$lang['select_source'] = 'Välj källa';
$lang['select_status'] = 'Välj status';
$lang['agents'] = 'Agenter';

// v1.4.2
$lang['read_only'] = 'Endast läsning';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';

$lang['send_location'] = 'Send Location';
$lang['search_for_a_place'] = 'Search for a place';
$lang['address_optional'] = 'Address (optional)';
$lang['latitude'] = 'Latitude';
$lang['longitude'] = 'Longitude';
$lang['drag_the_maker_to_set_the_location'] = 'Drag the marker to set the location or search for a place.';
$lang['use_my_location'] = 'Use My Current Location';
$lang['send_location'] = 'Send Location';

$lang['initiate_chat'] = 'Initiate Chat';
$lang['please_select_at_least_one_lead'] = 'Please select at least one lead';
$lang['chat_initiated_successfully'] = 'Chat Initiated  successfuly';
$lang['something_went_wrong'] = "Something Went Wrong";

$lang['video'] = "Video";
$lang['select_video'] = "Select Video";

$lang['whatsbot_cron'] = 'Whatsbot Cron';

// Session Management
$lang['session_management']                 = 'WhatsApp Session Management';
$lang['enable_session_management']          = 'Enable session management';
$lang['session_management_help_text']       = 'WhatsApp Cloud API has a 24-hour session limit for business-initiated messages. Enable this to send a reminder before the session expires.';
$lang['session_expiry_message']             = 'Session expiry message';
$lang['session_expiry_message_help']        = 'This message will be sent to customers before their 24-hour session expires.';
$lang['session_expiry_hours']               = 'Hours before expiry to send reminder';
$lang['session_expiry_hours_help']          = 'Set how many hours after the last customer message to send the reminder (max 23 hours).';
$lang['include_session_reset_button']       = 'Include quick reply button';
$lang['include_session_reset_button_help']  = 'Add a quick reply button to make it easier for customers to respond.';
$lang['session_management_note']            = 'Note: This feature requires the cron job to be properly configured. The cron job should run at least once per hour.';
$lang['session_reset_button_text']          = 'Continue Conversation';
$lang['whatsapp_session_management']        = 'WhatsApp Session Management';
$lang['active_sessions']                    = 'Active Sessions';
$lang['expiring_sessions']                  = 'Expiring Sessions';
$lang['sessions_reset_today']               = 'Sessions Reset Today';
$lang['sessions_reset_week']                = 'Sessions Reset (7 Days)';
$lang['session_management_disabled']        = 'WhatsApp Session Management is currently disabled.';

// Dashboard and Reports
$lang['session_reset_count']                = 'Session Reset Count';
$lang['session_expired']                    = 'Session Expired';
$lang['session_active']                     = 'Session Active';
$lang['session_reset_success_rate']         = 'Reset Success Rate';
$lang['customer_responded']                 = 'Customer Responded';
$lang['customer_not_responded']             = 'Customer Did Not Respond';
$lang['enable_now']                         = 'Enable Now';
