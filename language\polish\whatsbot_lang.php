<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Połącz konto';
$lang['connect_whatsapp_business'] = 'Połącz WhatsApp Business';
$lang['campaigning'] = 'Kampanowanie';
$lang['business_account_id_description'] = 'Twój identyfikator konta WhatsApp Business (WABA)';
$lang['access_token_description'] = 'Twój Token Dostępu Użytkownika po zarejestrowaniu konta w Portalu Deweloperów Facebooka';
$lang['whatsapp_business_account_id'] = 'Identyfikator konta WhatsApp Business';
$lang['whatsapp_access_token'] = 'Token dostępu WhatsApp';
$lang['webhook_callback_url'] = 'URL zwrotny Webhook';
$lang['verify_token'] = 'Zweryfikuj token';
$lang['connect'] = 'Połącz';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'Połączenie konta jednym kliknięciem';
$lang['connect_your_whatsapp_account'] = 'Połącz swoje konto WhatsApp';
$lang['copy'] = 'Kopiuj';
$lang['copied'] = 'Skopiowano!!';
$lang['disconnect'] = 'Rozłącz';
$lang['number'] = 'Numer';
$lang['number_id'] = 'Identyfikator numeru';
$lang['quality'] = 'Jakość';
$lang['status'] = 'Status';
$lang['business_account_id'] = 'Identyfikator konta biznesowego';
$lang['permissions'] = 'Uprawnienia';
$lang['phone_number_id_description'] = 'ID numeru telefonu połączonego z API WhatsApp Business. Jeśli nie jesteś pewien, możesz użyć żądania GET Phone Number ID, aby uzyskać je z API WhatsApp (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'ID numeru zarejestrowanego w WhatsApp';
$lang['update_details'] = 'Zaktualizuj szczegóły';

$lang['bots'] = 'Boty';
$lang['bots_management'] = 'Zarządzanie botami';
$lang['create_template_base_bot'] = 'Utwórz bazowego bota szablonowego';
$lang['create_message_bot'] = 'Utwórz bota wiadomości';
$lang['type'] = 'Typ';
$lang['message_bot'] = 'Bot wiadomości';
$lang['new_template_bot'] = 'Nowy bot szablonowy';
$lang['new_message_bot'] = 'Nowy bot wiadomości';
$lang['bot_name'] = 'Nazwa bota';
$lang['reply_text'] = 'Tekst odpowiedzi <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tekst, który zostanie wysłany do leada lub kontaktu. Możesz również użyć {companyname}, {crm_url} lub innych niestandardowych pól scalania leada lub kontaktu, lub użyć znaku \'@\' aby znaleźć dostępne pola scalania" data-placement="bottom"></i> <span class="text-muted">(Maksymalna dozwolona liczba znaków to 1024)</span>';
$lang['reply_type'] = 'Typ odpowiedzi';
$lang['trigger'] = 'Wyzwalacz';
$lang['header'] = 'Nagłówek';
$lang['footer_bot'] = 'Stopka <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maksymalna dozwolona liczba znaków to 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Opcja 1: Bot z przyciskami odpowiedzi';
$lang['bot_with_button_link'] = 'Opcja 2: Bot z przyciskiem linku - URL CTA';
$lang['button1'] = 'Przycisk1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maksymalna dozwolona liczba znaków to 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'Identyfikator przycisku1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maksymalna dozwolona liczba znaków to 256" data-placement="bottom"></i>';
$lang['button2'] = 'Przycisk2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maksymalna dozwolona liczba znaków to 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'Identyfikator przycisku2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maksymalna dozwolona liczba znaków to 256" data-placement="bottom"></i>';
$lang['button3'] = 'Przycisk3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maksymalna dozwolona liczba znaków to 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'Identyfikator przycisku3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maksymalna dozwolona liczba znaków to 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Nazwa przycisku <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maksymalna dozwolona liczba znaków to 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Link przycisku';
$lang['enter_name'] = 'Wprowadź nazwę';
$lang['select_reply_type'] = 'Wybierz typ odpowiedzi';
$lang['enter_bot_reply_trigger'] = 'Wprowadź wyzwalacz odpowiedzi bota';
$lang['enter_header'] = 'Wprowadź nagłówek';
$lang['enter_footer'] = 'Wprowadź stopkę';
$lang['enter_button1'] = 'Wprowadź przycisk1';
$lang['enter_button1_id'] = 'Wprowadź identyfikator przycisku1';
$lang['enter_button2'] = 'Wprowadź przycisk2';
$lang['enter_button2_id'] = 'Wprowadź identyfikator przycisku2';
$lang['enter_button3'] = 'Wprowadź przycisk3';
$lang['enter_button3_id'] = 'Wprowadź identyfikator przycisku3';
$lang['enter_button_name'] = 'Wprowadź nazwę przycisku';
$lang['enter_button_url'] = 'Wprowadź URL przycisku';
$lang['on_exact_match'] = 'Bot odpowiedzi: Przy pełnym dopasowaniu';
$lang['when_message_contains'] = 'Bot odpowiedzi: Gdy wiadomość zawiera';
$lang['when_client_send_the_first_message'] = 'Odpowiedź powitalna - gdy lead lub klient wysyła pierwszą wiadomość';
$lang['bot_create_successfully'] = 'Bot utworzony pomyślnie';
$lang['bot_update_successfully'] = 'Bot zaktualizowany pomyślnie';
$lang['bot_deleted_successfully'] = 'Bot usunięty pomyślnie';
$lang['templates'] = 'Szablony';
$lang['template_data_loaded'] = 'Szablony załadowane pomyślnie';
$lang['load_templates'] = 'Załaduj szablony';
$lang['template_management'] = 'Zarządzanie szablonami';


// campaigns
$lang['campaign'] = 'Kampania';
$lang['campaigns'] = 'Kampanie';
$lang['send_new_campaign'] = 'Wyślij nową kampanię';
$lang['campaign_name'] = 'Nazwa kampanii';
$lang['template'] = 'Szablon';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Dla klienta, w oparciu o strefę czasową kontaktu" data-placement="left"></i>Zaplanowany czas wysyłki';
$lang['scheduled_time_description'] = 'Dla klienta, w oparciu o strefę czasową kontaktu';
$lang['ignore_scheduled_time_and_send_now'] = 'Ignoruj zaplanowany czas i wyślij teraz';
$lang['template'] = 'Szablon';
$lang['leads'] = 'Leady';
$lang['delivered_to'] = 'Dostarczono do';
$lang['read_by'] = 'Przeczytane przez';
$lang['variables'] = 'Zmienne';
$lang['body'] = 'Treść';
$lang['variable'] = 'Zmienna';
$lang['match_with_selected_field'] = 'Dopasuj z wybranym polem';
$lang['preview'] = 'Podgląd';
$lang['send_campaign'] = 'Wyślij kampanię';
$lang['send_to'] = 'Wyślij do';
$lang['send_campaign'] = 'Wyślij kampanię';
$lang['view_campaign'] = 'Zobacz kampanię';
$lang['campaign_daily_task'] = 'Codzienna zadanie kampanii';
$lang['back'] = 'Wstecz';
$lang['phone'] = 'Telefon';
$lang['message'] = 'Wiadomość';
$lang['currently_type_not_supported'] = 'Obecnie <strong> %s </strong> typ szablonu nie jest obsługiwany!';
$lang['of_your'] = 'twoich ';
$lang['contacts'] = 'Kontaktów';
$lang['select_all_leads'] = 'Zaznacz wszystkie leady';
$lang['select_all_note_leads'] = 'Jeśli zaznaczysz to, wszystkie przyszłe leady są uwzględnione w tej kampanii.';
$lang['select_all_note_contacts'] = 'Jeśli zaznaczysz to, wszystkie przyszłe kontakty są uwzględnione w tej kampanii.';

$lang['verified_name'] = 'Zweryfikowana nazwa';
$lang['mark_as_default'] = 'Oznacz jako domyślne';
$lang['default_number_updated'] = 'Domyślny identyfikator numeru telefonu został pomyślnie zaktualizowany';
$lang['currently_using_this_number'] = 'Obecnie używasz tego numeru';
$lang['leads'] = 'Leady';
$lang['pause_campaign'] = 'Wstrzymaj kampanię';
$lang['resume_campaign'] = 'Wznów kampanię';
$lang['campaign_resumed'] = 'Kampania wznowiona';
$lang['campaign_paused'] = 'Kampania wstrzymana';

//Template
$lang['body_data'] = 'Dane treści';
$lang['category'] = 'Kategoria';

// Template bot
$lang['create_new_template_bot'] = 'Utwórz nowego bota szablonu';
$lang['template_bot'] = 'Bot szablonu';
$lang['variables'] = 'Zmienne';
$lang['preview'] = 'Podgląd';
$lang['template'] = 'Szablon';
$lang['bot_content_1'] = 'Ta wiadomość zostanie wysłana do kontaktu, gdy spełnione zostaną zasady wyzwalania w wiadomości wysłanej przez kontakt.';
$lang['save_bot'] = 'Zapisz bota';
$lang['please_select_template'] = 'Proszę wybrać szablon';
$lang['use_manually_define_value'] = 'Użyj ręcznie zdefiniowanej wartości';
$lang['merge_fields'] = 'Pola scalania';
$lang['template_bot_create_successfully'] = 'Bot szablonu został pomyślnie utworzony';
$lang['template_bot_update_successfully'] = 'Bot szablonu został pomyślnie zaktualizowany';
$lang['text_bot'] = 'Bot tekstowy';
$lang['option_2_bot_with_link'] = 'Opcja 2: Bot z linkiem przycisku - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Opcja 3: Bot z plikiem';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Wiadomość wysyłana, gdy oczekiwany jest opóźnienie w odpowiedzi';
$lang['bot_delay_response_placeholder'] = 'Daj mi chwilę, wkrótce będę miał odpowiedź';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Typ relacji';
$lang['select_all'] = 'Zaznacz wszystko';
$lang['total'] = 'Całkowity';
$lang['merge_field_note'] = 'Użyj znaku \'@\' do dodania pól scalania.';
$lang['send_to_all'] = 'Wyślij do wszystkich ';
$lang['or'] = 'LUB';

$lang['convert_whatsapp_message_to_lead'] = 'Automatyczne pozyskiwanie nowych leadów (przekształć nowe wiadomości WhatsApp w leady)';
$lang['leads_status'] = 'Status leadów';
$lang['leads_assigned'] = 'Lead przydzielony';
$lang['whatsapp_auto_lead'] = 'Whatsapp Auto Lead';
$lang['webhooks_label'] = 'Otrzymane dane WhatsApp będą ponownie wysyłane do';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Włącz ponowne wysyłanie WebHooks';
$lang['chat'] = 'Czat';
$lang['black_listed_phone_numbers'] = 'Czarne numery telefonów';
$lang['sent_status'] = 'Status wysłania';

$lang['active'] = 'Aktywny';
$lang['approved'] = 'Zatwierdzony';
$lang['this_month'] = 'ten miesiąc';
$lang['open_chats'] = 'Otwarte czaty';
$lang['resolved_conversations'] = 'Rozwiązane rozmowy';
$lang['messages_sent'] = 'Wiadomości wysłane';
$lang['account_connected'] = 'Konto połączone';
$lang['account_disconnected'] = 'Konto odłączone';
$lang['webhook_verify_token'] = 'Token weryfikacji webhooku';
// Chat integration
$lang['chat_message_note'] = 'Wiadomość zostanie wkrótce wysłana. Proszę pamiętać, że jeśli nowy kontakt, nie pojawi się na tej liście, aż kontakt zacznie z Tobą współdziałać!';

$lang['activity_log'] = 'Dziennik aktywności';
$lang['whatsapp_logs'] = 'Logi WhatsApp';
$lang['response_code'] = 'Kod odpowiedzi';
$lang['recorded_on'] = 'Zarejestrowano w';

$lang['request_details'] = 'Szczegóły żądania';
$lang['raw_content'] = 'Surowa zawartość';
$lang['headers'] = 'Nagłówki';
$lang['format_type'] = 'Typ formatu';

// Permission section
$lang['show_campaign'] = 'Pokaż kampanię';
$lang['clear_log'] = 'Wyczyść dziennik';
$lang['log_activity'] = 'Zapisz aktywność';
$lang['load_template'] = 'Załaduj szablon';

$lang['action'] = 'Akcja';
$lang['total_parameters'] = 'Całkowita liczba parametrów';
$lang['template_name'] = 'Nazwa szablonu';
$lang['log_cleared_successfully'] = 'Dziennik został pomyślnie wyczyszczony';
$lang['whatsbot_stats'] = 'Statystyki WhatsBot';

$lang['not_found_or_deleted'] = 'Nie znaleziono lub usunięto';
$lang['response'] = 'Odpowiedź';

$lang['select_image'] = 'Wybierz obraz';
$lang['image'] = 'Obraz';
$lang['image_deleted_successfully'] = 'Obraz został pomyślnie usunięty';
$lang['whatsbot_settings'] = 'Ustawienia Whatsbot';
$lang['maximum_file_size_should_be'] = 'Maksymalny rozmiar pliku powinien wynosić ';
$lang['allowed_file_types'] = 'Dozwolone typy plików : ';

$lang['send_image'] = 'Wyślij obraz';
$lang['send_video'] = 'Wyślij wideo';
$lang['send_document'] = 'Wyślij dokument';
$lang['record_audio'] = 'Nagraj audio';
$lang['chat_media_info'] = 'Więcej informacji na temat obsługiwanych typów zawartości i przetwarzania mediów';
$lang['help'] = 'Pomoc';


// v1.1.0
$lang['clone'] = 'Klona';
$lang['bot_clone_successfully'] = 'Bot został pomyślnie sklonowany';
$lang['all_chat'] = 'Wszystkie czaty';
$lang['from'] = 'Od:';
$lang['phone_no'] = 'Numer telefonu:';
$lang['supportagent'] = 'Agent wsparcia';
$lang['assign_chat_permission_to_support_agent'] = 'Przyznaj uprawnienia czatu tylko agentowi wsparcia';
$lang['enable_whatsapp_notification_sound'] = 'Włącz dźwięk powiadomień czatu WhatsApp';
$lang['notification_sound'] = 'Dźwięk powiadomienia';
$lang['trigger_keyword'] = 'Słowo kluczowe wyzwalające';
$lang['modal_title'] = 'Wybierz agenta wsparcia';
$lang['close_btn'] = 'Zamknij';
$lang['save_btn'] = 'Zapisz';
$lang['support_agent'] = 'Agent wsparcia';
$lang['change_support_agent'] = 'Zmień agenta wsparcia';
$lang['replay_message'] = 'Nie możesz wysłać wiadomości, minęło 24 godziny.';
$lang['support_agent_note'] = '<strong>Uwaga: </strong> Po włączeniu funkcji agenta wsparcia, przypisany lead automatycznie zostanie przypisany do czatu. Administratorzy mogą również przypisać nowego agenta z strony czatu.';
$lang['permission_bot_clone'] = 'Klona bota';
$lang['remove_chat'] = 'Usuń czat';
$lang['default_message_on_no_match'] = 'Domyślna odpowiedź - jeśli żadne słowo kluczowe nie pasuje';
$lang['default_message_note'] = '<strong>Uwaga: </strong> Włączenie tej opcji zwiększy obciążenie twojego webhooka. Po więcej informacji odwiedź ten <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">link</a>.';

$lang['whatsbot_connect_account'] = 'Połącz konto Whatsbot';
$lang['whatsbot_message_bot'] = 'Bot wiadomości Whatsbot';
$lang['whatsbot_template_bot'] = 'Szablon bota Whatsbot';
$lang['whatsbot_template'] = 'Szablon Whatsbot';
$lang['whatsbot_campaigns'] = 'Kampanie Whatsbot';
$lang['whatsbot_chat'] = 'Czat Whatsbot';
$lang['whatsbot_log_activity'] = 'Dziennik aktywności Whatsbot';
$lang['message_templates_not_exists_note'] = 'Brak uprawnienia do szablonu meta. Proszę włączyć to w swoim koncie Meta';

// v1.2.0
$lang['ai_prompt'] = 'AI Podpowiedzi';
$lang['ai_prompt_note'] = 'Aby włączyć podpowiedzi AI, wprowadź wiadomość, aby włączyć funkcję, lub użyj podpowiedzi AI, jeśli już są włączone';
$lang['emojis'] = 'Emotikony';
$lang['translate'] = 'Tłumacz';
$lang['change_tone'] = 'Zmień ton';
$lang['professional'] = 'Profesjonalny';
$lang['friendly'] = 'Przyjazny';
$lang['empathetic'] = 'Empatyczny';
$lang['straightforward'] = 'Bezpośredni';
$lang['simplify_language'] = 'Uprość język';
$lang['fix_spelling_and_grammar'] = 'Popraw ortografię i gramatykę';

$lang['ai_integration'] = 'Integracja AI';
$lang['open_ai_api'] = 'API OpenAI';
$lang['open_ai_secret_key'] = 'Sekretny klucz OpenAI - <a href="https://platform.openai.com/account/api-keys" target="_blank">Gdzie możesz znaleźć sekretny klucz?</a>';
$lang['chat_text_limit'] = 'Limit tekstu czatu';
$lang['chat_text_limit_note'] = 'Aby zoptymalizować koszty operacyjne, rozważ ograniczenie liczby słów odpowiedzi czatu OpenAI';
$lang['chat_model'] = 'Model czatu';
$lang['openai_organizations'] = 'Organizacje OpenAi';
$lang['template_type'] = 'Typ szablonu';
$lang['update'] = 'Aktualizuj';
$lang['open_ai_key_verification_fail'] = 'Weryfikacja klucza OpenAi oczekuje na ustawienia, proszę połączyć swoje konto openai';
$lang['enable_wb_openai'] = 'Włącz OpenAI w czacie';
$lang['webhook_resend_method'] = 'Metoda ponownego wysyłania webhooków';
$lang['search_language'] = 'Szukaj języka...';
$lang['document'] = 'Dokument';
$lang['select_document'] = 'Wybierz dokument';
$lang['attchment_deleted_successfully'] = 'Załącznik usunięty pomyślnie';
$lang['attach_image_video_docs'] = 'Dołącz obraz, wideo, dokumenty';
$lang['choose_file_type'] = 'Wybierz typ pliku';
$lang['max_size'] = 'Maksymalny rozmiar: ';

// v1.3.0

// Import CSV
$lang['bulk_campaigns'] = 'Kampanie masowe';
$lang['upload_csv'] = 'Prześlij CSV';
$lang['upload'] = 'Prześlij';
$lang['csv_uploaded_successfully'] = 'Plik CSV przesłany pomyślnie';
$lang['please_select_file'] = 'Proszę wybrać plik CSV';
$lang['phonenumber_field_is_required'] = 'Pole numeru telefonu jest wymagane';
$lang['out_of_the'] = 'Z';
$lang['records_in_your_csv_file'] = 'rekordy w twoim pliku CSV,';
$lang['valid_the_campaign_can_be_sent'] = 'rekordy są ważne.<br /> Kampania może zostać pomyślnie wysłana do tych';
$lang['users'] = 'użytkowników';
$lang['campaigns_from_csv_file'] = 'Kampanie z pliku CSV';
$lang['download_sample'] = 'Pobierz próbkę';
$lang['csv_rule_1'] = '1. <b>Wymóg kolumny numeru telefonu:</b> Twój plik CSV musi zawierać kolumnę o nazwie "Phoneno." Każdy rekord w tej kolumnie powinien zawierać ważny numer kontaktowy, poprawnie sformatowany z kodem kraju, w tym znak "+" .<br /><br />';
$lang['csv_rule_2'] = '2. <b>Format i kodowanie CSV:</b> Twoje dane CSV powinny przestrzegać określonego formatu. Pierwszy wiersz twojego pliku CSV musi zawierać nagłówki kolumn, jak pokazano w przykładowej tabeli. Upewnij się, że twój plik jest kodowany w UTF-8, aby uniknąć jakichkolwiek problemów z kodowaniem.';
$lang['please_upload_valid_csv_file'] = 'Proszę przesłać prawidłowy plik CSV';
$lang['please_add_valid_number_in_csv_file'] = 'Proszę dodać prawidłowy <b>Phoneno</b> w pliku CSV';
$lang['total_send_campaign_list'] = 'Całkowita wysłana kampania: %s';
$lang['sample_data'] = 'Dane przykładowe';
$lang['firstname'] = 'Imię';
$lang['lastname'] = 'Nazwisko';
$lang['phoneno'] = 'Numer telefonu';
$lang['email'] = 'E-mail';
$lang['country'] = 'Kraj';
$lang['download_sample_and_read_rules'] = 'Pobierz plik próbny i przeczytaj zasady';
$lang['please_wait_your_request_in_process'] = 'Proszę czekać, twoje żądanie jest obecnie przetwarzane.';
$lang['whatsbot_bulk_campaign'] = 'Kampanie masowe Whatsbot';
$lang['csv_campaign'] = 'Kampania CSV';

// Wstępne odpowiedzi
$lang['canned_reply'] = 'Gotowa odpowiedź';
$lang['canned_reply_menu'] = 'Gotowa odpowiedź';
$lang['create_canned_reply'] = 'Utwórz gotową odpowiedź';
$lang['title'] = 'Tytuł';
$lang['desc'] = 'Opis';
$lang['public'] = 'Publiczny';
$lang['action'] = 'Akcja';
$lang['delete_successfully'] = 'Odpowiedź usunięta.';
$lang['cannot_delete'] = 'Nie można usunąć odpowiedzi.';
$lang['whatsbot_canned_reply'] = 'Gotowe odpowiedzi Whatsbot';
$lang['reply'] = 'Odpowiedź';

// Podpowiedzi AI
$lang['ai_prompts'] = 'Podpowiedzi AI';
$lang['create_ai_prompts'] = 'Utwórz podpowiedzi AI';
$lang['name'] = 'Nazwa';
$lang['action'] = 'Akcja';
$lang['prompt_name'] = 'Nazwa podpowiedzi';
$lang['prompt_action'] = 'Akcja podpowiedzi';
$lang['whatsbot_ai_prompts'] = 'Podpowiedzi AI Whatsbot';

// Nowy czat
$lang['replying_to'] = 'Odpowiadam na:';
$lang['download_document'] = 'Pobierz dokument';
$lang['custom_prompt'] = 'Własna podpowiedź';
$lang['canned_replies'] = 'Gotowe odpowiedzi';
$lang['use_@_to_add_merge_fields'] = 'Użyj \'@\' aby dodać pola scalania';
$lang['type_your_message'] = 'Napisz swoją wiadomość';
$lang['you_cannot_send_a_message_using_this_number'] = 'Nie możesz wysłać wiadomości, używając tego numeru.';

// Przepływ bota
$lang['bot_flow'] = 'Przepływ bota';
$lang['create_new_flow'] = 'Utwórz nowy przepływ';
$lang['flow_name'] = 'Nazwa przepływu';
$lang['flow'] = 'Przepływ';
$lang['bot_flow_builder'] = 'Kreator przepływu bota';
$lang['you_can_not_upload_file_type'] = 'Nie możesz przesłać pliku typu <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Przepływ bota Whatsbot';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Automatyczne czyszczenie historii czatu';
$lang['enable_auto_clear_chat_history'] = 'Włącz automatyczne czyszczenie historii czatu';
$lang['auto_clear_time'] = 'Czas automatycznego czyszczenia historii';
$lang['clear_chat_history_note'] = '<strong>Uwaga: </strong> Jeśli włączysz funkcję automatycznego czyszczenia historii czatu, automatycznie usunie ona historię czatu na podstawie liczby dni, które określisz, gdy tylko uruchomi się zadanie cron.';
$lang['source'] = 'Źródło';
$lang['groups'] = 'Grupy';

// v1.3.3
$lang['click_user_to_chat'] = 'Kliknij użytkownika, aby rozpocząć czat';
$lang['searching'] = 'Wyszukiwanie...';
$lang['filters'] = 'Filtry';
$lang['relation_type'] = 'Typ relacji';
$lang['groups'] = 'Grupy';
$lang['source'] = 'Źródło';
$lang['status'] = 'Status';
$lang['select_type'] = 'Wybierz typ';
$lang['select_agents'] = 'Wybierz agentów';
$lang['select_group'] = 'Wybierz grupę';
$lang['select_source'] = 'Wybierz źródło';
$lang['select_status'] = 'Wybierz status';
$lang['agents'] = 'Agenci';

// v1.4.2
$lang['read_only'] = 'Tylko do odczytu';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';

$lang['send_location'] = 'Send Location';
$lang['search_for_a_place'] = 'Search for a place';
$lang['address_optional'] = 'Address (optional)';
$lang['latitude'] = 'Latitude';
$lang['longitude'] = 'Longitude';
$lang['drag_the_maker_to_set_the_location'] = 'Drag the marker to set the location or search for a place.';
$lang['use_my_location'] = 'Use My Current Location';
$lang['send_location'] = 'Send Location';

$lang['initiate_chat'] = 'Initiate Chat';
$lang['please_select_at_least_one_lead'] = 'Please select at least one lead';
$lang['chat_initiated_successfully'] = 'Chat Initiated  successfuly';
$lang['something_went_wrong'] = "Something Went Wrong";

$lang['video'] = "Video";
$lang['select_video'] = "Select Video";

$lang['whatsbot_cron'] = 'Whatsbot Cron';

// Session Management
$lang['session_management']                 = 'WhatsApp Session Management';
$lang['enable_session_management']          = 'Enable session management';
$lang['session_management_help_text']       = 'WhatsApp Cloud API has a 24-hour session limit for business-initiated messages. Enable this to send a reminder before the session expires.';
$lang['session_expiry_message']             = 'Session expiry message';
$lang['session_expiry_message_help']        = 'This message will be sent to customers before their 24-hour session expires.';
$lang['session_expiry_hours']               = 'Hours before expiry to send reminder';
$lang['session_expiry_hours_help']          = 'Set how many hours after the last customer message to send the reminder (max 23 hours).';
$lang['include_session_reset_button']       = 'Include quick reply button';
$lang['include_session_reset_button_help']  = 'Add a quick reply button to make it easier for customers to respond.';
$lang['session_management_note']            = 'Note: This feature requires the cron job to be properly configured. The cron job should run at least once per hour.';
$lang['session_reset_button_text']          = 'Continue Conversation';
$lang['whatsapp_session_management']        = 'WhatsApp Session Management';
$lang['active_sessions']                    = 'Active Sessions';
$lang['expiring_sessions']                  = 'Expiring Sessions';
$lang['sessions_reset_today']               = 'Sessions Reset Today';
$lang['sessions_reset_week']                = 'Sessions Reset (7 Days)';
$lang['session_management_disabled']        = 'WhatsApp Session Management is currently disabled.';

// Dashboard and Reports
$lang['session_reset_count']                = 'Session Reset Count';
$lang['session_expired']                    = 'Session Expired';
$lang['session_active']                     = 'Session Active';
$lang['session_reset_success_rate']         = 'Reset Success Rate';
$lang['customer_responded']                 = 'Customer Responded';
$lang['customer_not_responded']             = 'Customer Did Not Respond';
$lang['enable_now']                         = 'Enable Now';
