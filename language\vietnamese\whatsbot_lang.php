<?php

defined('BASEPATH') || exit('No direct script access allowed');

// kết nối tài khoản
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Kết Nối <PERSON>à<PERSON>';
$lang['connect_whatsapp_business'] = 'Kết Nối WhatsApp Business';
$lang['campaigning'] = 'Chạy Chiến Dịch';
$lang['business_account_id_description'] = 'ID Tài Khoản WhatsApp Business (WABA) của bạn';
$lang['access_token_description'] = 'Mã Truy Cập của bạn sau khi đăng ký tài khoản trên Facebook Developers Portal';
$lang['whatsapp_business_account_id'] = 'ID Tài Khoản WhatsApp Business';
$lang['whatsapp_access_token'] = 'Mã Truy Cập WhatsApp';
$lang['webhook_callback_url'] = 'URL Gọi Lại Webhook';
$lang['verify_token'] = '<PERSON><PERSON><PERSON>';
$lang['connect'] = 'Kết Nối';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'Kết Nối Tài Khoản Chỉ Với Một Cú Nhấp';
$lang['connect_your_whatsapp_account'] = 'Kết Nối Tài Khoản WhatsApp Của Bạn';
$lang['copy'] = 'Sao Chép';
$lang['copied'] = 'Đã Sao Chép!!';
$lang['disconnect'] = 'Ngắt Kết Nối';
$lang['number'] = 'Số';
$lang['number_id'] = 'ID Số';
$lang['quality'] = 'Chất Lượng';
$lang['status'] = 'Trạng Thái';
$lang['business_account_id'] = 'ID Tài Khoản Kinh Doanh';
$lang['permissions'] = 'Quyền';
$lang['phone_number_id_description'] = 'ID của số điện thoại kết nối với API WhatsApp Business. Nếu bạn không chắc, bạn có thể sử dụng yêu cầu GET Phone Number ID để truy xuất từ WhatsApp API ( https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers )';
$lang['phone_number_id'] = 'ID Số Điện Thoại Đã Đăng Ký WhatsApp';
$lang['update_details'] = 'Cập Nhật Chi Tiết';

$lang['bots'] = 'Bots';
$lang['bots_management'] = 'Quản Lý Bots';
$lang['create_template_base_bot'] = 'Tạo bot dựa trên mẫu';
$lang['create_message_bot'] = 'Tạo bot tin nhắn';
$lang['type'] = 'Loại';
$lang['message_bot'] = 'Bot Tin Nhắn';
$lang['new_template_bot'] = 'Bot Mẫu Mới';
$lang['new_message_bot'] = 'Bot Tin Nhắn Mới';
$lang['bot_name'] = 'Tên Bot';
$lang['reply_text'] = 'Nội Dung Trả Lời <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Văn bản sẽ được gửi đến lead hoặc contact. Bạn cũng có thể sử dụng {companyname}, {crm_url} hoặc các trường tùy chỉnh khác của lead hoặc contact, hoặc sử dụng ký hiệu \'@\' để tìm các trường hợp nhập liệu" data-placement="bottom"></i> <span class="text-muted">(Tối đa 1024 ký tự)</span>';
$lang['reply_type'] = 'Loại Trả Lời';
$lang['trigger'] = 'Kích Hoạt';
$lang['header'] = 'Tiêu Đề';
$lang['footer_bot'] = 'Chân Trang <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tối đa 60 ký tự" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Tùy Chọn 1: Bot với nút trả lời';
$lang['bot_with_button_link'] = 'Tùy Chọn 2: Bot với nút liên kết - URL CTA';
$lang['button1'] = 'Nút1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tối đa 20 ký tự" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID Nút1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tối đa 256 ký tự" data-placement="bottom"></i>';
$lang['button2'] = 'Nút2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tối đa 20 ký tự" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID Nút2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tối đa 256 ký tự" data-placement="bottom"></i>';
$lang['button3'] = 'Nút3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tối đa 20 ký tự" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID Nút3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tối đa 256 ký tự" data-placement="bottom"></i>';
$lang['button_name'] = 'Tên Nút <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tối đa 20 ký tự" data-placement="bottom"></i>';
$lang['button_link'] = 'Liên Kết Nút';
$lang['enter_name'] = 'Nhập Tên';
$lang['select_reply_type'] = 'Chọn loại trả lời';
$lang['enter_bot_reply_trigger'] = 'Nhập kích hoạt trả lời bot';
$lang['enter_header'] = 'Nhập tiêu đề';
$lang['enter_footer'] = 'Nhập chân trang';
$lang['enter_button1'] = 'Nhập Nút1';
$lang['enter_button1_id'] = 'Nhập ID Nút1';
$lang['enter_button2'] = 'Nhập Nút2';
$lang['enter_button2_id'] = 'Nhập ID Nút2';
$lang['enter_button3'] = 'Nhập Nút3';
$lang['enter_button3_id'] = 'Nhập ID Nút3';
$lang['enter_button_name'] = 'Nhập tên nút';
$lang['enter_button_url'] = 'Nhập URL nút';
$lang['on_exact_match'] = 'Bot trả lời: Khi khớp chính xác';
$lang['when_message_contains'] = 'Bot trả lời: Khi tin nhắn chứa';
$lang['when_client_send_the_first_message'] = 'Trả lời chào mừng - khi lead hoặc khách hàng gửi tin nhắn đầu tiên';
$lang['bot_create_successfully'] = 'Bot đã được tạo thành công';
$lang['bot_update_successfully'] = 'Bot đã được cập nhật thành công';
$lang['bot_deleted_successfully'] = 'Bot đã được xóa thành công';
$lang['templates'] = 'Mẫu';
$lang['template_data_loaded'] = 'Dữ liệu mẫu đã được tải thành công';
$lang['load_templates'] = 'Tải Mẫu';
$lang['template_management'] = 'Quản Lý Mẫu';

// campaigns
$lang['campaign'] = 'Chiến dịch';
$lang['campaigns'] = 'Các chiến dịch';
$lang['send_new_campaign'] = 'Gửi chiến dịch mới';
$lang['campaign_name'] = 'Tên chiến dịch';
$lang['template'] = 'Mẫu';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Theo khách hàng, dựa trên múi giờ của liên hệ" data-placement="left"></i>Thời gian gửi theo lịch trình';
$lang['scheduled_time_description'] = 'Theo khách hàng, dựa trên múi giờ của liên hệ';
$lang['ignore_scheduled_time_and_send_now'] = 'Bỏ qua thời gian đã lên lịch và gửi ngay bây giờ';
$lang['template'] = 'Mẫu';
$lang['leads'] = 'Khách hàng tiềm năng';
$lang['delivered_to'] = 'Đã gửi tới';
$lang['read_by'] = 'Đã đọc bởi';
$lang['variables'] = 'Biến';
$lang['body'] = 'Nội dung';
$lang['variable'] = 'Biến';
$lang['match_with_selected_field'] = 'Khớp với một trường đã chọn';
$lang['preview'] = 'Xem trước';
$lang['send_campaign'] = 'Gửi chiến dịch';
$lang['send_to'] = 'Gửi tới';
$lang['send_campaign'] = 'Gửi chiến dịch';
$lang['view_campaign'] = 'Xem chiến dịch';
$lang['campaign_daily_task'] = 'Nhiệm vụ hàng ngày của chiến dịch';
$lang['back'] = 'Quay lại';
$lang['phone'] = 'Điện thoại';
$lang['message'] = 'Tin nhắn';
$lang['currently_type_not_supported'] = 'Hiện tại loại mẫu <strong> %s </strong> không được hỗ trợ!';
$lang['of_your'] = 'của bạn ';
$lang['contacts'] = 'Liên hệ';
$lang['select_all_leads'] = 'Chọn tất cả khách hàng tiềm năng';
$lang['select_all_note_leads'] = 'Nếu bạn chọn điều này, tất cả khách hàng tiềm năng trong tương lai sẽ được đưa vào chiến dịch này.';
$lang['select_all_note_contacts'] = 'Nếu bạn chọn điều này, tất cả liên hệ trong tương lai sẽ được đưa vào chiến dịch này.';

$lang['verified_name'] = 'Tên đã xác minh';
$lang['mark_as_default'] = 'Đánh dấu là mặc định';
$lang['default_number_updated'] = 'ID số điện thoại mặc định đã được cập nhật thành công';
$lang['currently_using_this_number'] = 'Hiện tại đang sử dụng số này';
$lang['leads'] = 'Khách hàng tiềm năng';
$lang['pause_campaign'] = 'Tạm dừng chiến dịch';
$lang['resume_campaign'] = 'Tiếp tục chiến dịch';
$lang['campaign_resumed'] = 'Chiến dịch đã được tiếp tục';
$lang['campaign_paused'] = 'Chiến dịch đã bị tạm dừng';

//Template
$lang['body_data'] = 'Dữ liệu nội dung';
$lang['category'] = 'Danh mục';

// Template bot
$lang['create_new_template_bot'] = 'Tạo bot mẫu mới';
$lang['template_bot'] = 'Bot mẫu';
$lang['variables'] = 'Biến';
$lang['preview'] = 'Xem trước';
$lang['template'] = 'Mẫu';
$lang['bot_content_1'] = 'Tin nhắn này sẽ được gửi tới liên hệ khi quy tắc kích hoạt được đáp ứng trong tin nhắn được gửi bởi liên hệ.';
$lang['save_bot'] = 'Lưu bot';
$lang['please_select_template'] = 'Vui lòng chọn một mẫu';
$lang['use_manually_define_value'] = 'Sử dụng giá trị được định nghĩa thủ công';
$lang['merge_fields'] = 'Trường hợp hợp nhất';
$lang['template_bot_create_successfully'] = 'Bot mẫu đã được tạo thành công';
$lang['template_bot_update_successfully'] = 'Bot mẫu đã được cập nhật thành công';
$lang['text_bot'] = 'Bot văn bản';
$lang['option_2_bot_with_link'] = 'Tùy chọn 2: Bot với nút liên kết - URL kêu gọi hành động (CTA)';
$lang['option_3_file'] = 'Tùy chọn 3: Bot với tệp';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Tin nhắn sẽ được gửi khi có sự chậm trễ trong phản hồi';
$lang['bot_delay_response_placeholder'] = 'Cho tôi một chút thời gian, tôi sẽ có câu trả lời ngay';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Loại quan hệ';
$lang['select_all'] = 'Chọn tất cả';
$lang['total'] = 'Tổng';
$lang['merge_field_note'] = 'Sử dụng dấu \'@\' để thêm trường hợp hợp nhất.';
$lang['send_to_all'] = 'Gửi đến tất cả ';
$lang['or'] = 'HOẶC';

$lang['convert_whatsapp_message_to_lead'] = 'Tự động thu hút khách hàng tiềm năng mới (chuyển đổi tin nhắn WhatsApp mới thành khách hàng tiềm năng)';
$lang['leads_status'] = 'Trạng thái khách hàng tiềm năng';
$lang['leads_assigned'] = 'Khách hàng tiềm năng được phân công';
$lang['whatsapp_auto_lead'] = 'Khách hàng tiềm năng tự động WhatsApp';
$lang['webhooks_label'] = 'Dữ liệu nhận được từ WhatsApp sẽ được gửi lại tới';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Kích hoạt gửi lại WebHooks';
$lang['chat'] = 'Trò chuyện';
$lang['black_listed_phone_numbers'] = 'Số điện thoại bị chặn';
$lang['sent_status'] = 'Trạng thái đã gửi';

$lang['active'] = 'Hoạt động';
$lang['approved'] = 'Được phê duyệt';
$lang['this_month'] = 'tháng này';
$lang['open_chats'] = 'Cuộc trò chuyện mở';
$lang['resolved_conversations'] = 'Cuộc trò chuyện đã giải quyết';
$lang['messages_sent'] = 'Tin nhắn đã gửi';
$lang['account_connected'] = 'Tài khoản đã kết nối';
$lang['account_disconnected'] = 'Tài khoản đã ngắt kết nối';
$lang['webhook_verify_token'] = 'Mã xác minh Webhook';
// Chat integration
$lang['chat_message_note'] = 'Tin nhắn sẽ được gửi sớm. Vui lòng lưu ý rằng nếu liên hệ mới, nó sẽ không xuất hiện trong danh sách này cho đến khi liên hệ bắt đầu tương tác với bạn!';

$lang['activity_log'] = 'Nhật ký hoạt động';
$lang['whatsapp_logs'] = 'Nhật ký WhatsApp';
$lang['response_code'] = 'Mã phản hồi';
$lang['recorded_on'] = 'Ghi lại vào';

$lang['request_details'] = 'Chi tiết yêu cầu';
$lang['raw_content'] = 'Nội dung thô';
$lang['headers'] = 'Tiêu đề';
$lang['format_type'] = 'Loại định dạng';

// Permission section
$lang['show_campaign'] = 'Hiển thị chiến dịch';
$lang['clear_log'] = 'Xóa nhật ký';
$lang['log_activity'] = 'Ghi nhật ký hoạt động';
$lang['load_template'] = 'Tải mẫu';

$lang['action'] = 'Hành động';
$lang['total_parameters'] = 'Tổng số tham số';
$lang['template_name'] = 'Tên mẫu';
$lang['log_cleared_successfully'] = 'Nhật ký đã được xóa thành công';
$lang['whatsbot_stats'] = 'Thống kê WhatsBot';

$lang['not_found_or_deleted'] = 'Không tìm thấy hoặc đã bị xóa';
$lang['response'] = 'Phản hồi';

$lang['select_image'] = 'Chọn hình ảnh';
$lang['image'] = 'Hình ảnh';
$lang['image_deleted_successfully'] = 'Hình ảnh đã được xóa thành công';
$lang['whatsbot_settings'] = 'Cài đặt Whatsbot';
$lang['maximum_file_size_should_be'] = 'Kích thước tệp tối đa nên là ';
$lang['allowed_file_types'] = 'Các loại tệp được phép: ';

$lang['send_image'] = 'Gửi hình ảnh';
$lang['send_video'] = 'Gửi video';
$lang['send_document'] = 'Gửi tài liệu';
$lang['record_audio'] = 'Ghi âm';
$lang['chat_media_info'] = 'Thêm thông tin cho Các loại nội dung được hỗ trợ & Kích thước xử lý tệp';
$lang['help'] = 'Giúp';

// v1.1.0
$lang['clone'] = 'Nhân bản';
$lang['bot_clone_successfully'] = 'Bot đã nhân bản thành công';
$lang['all_chat'] = 'Tất cả cuộc trò chuyện';
$lang['from'] = 'Từ:';
$lang['phone_no'] = 'Số điện thoại:';
$lang['supportagent'] = 'Nhân viên hỗ trợ';
$lang['assign_chat_permission_to_support_agent'] = 'Gán quyền trò chuyện cho nhân viên hỗ trợ';
$lang['enable_whatsapp_notification_sound'] = 'Kích hoạt âm thanh thông báo trò chuyện WhatsApp';
$lang['notification_sound'] = 'Âm thanh thông báo';
$lang['trigger_keyword'] = 'Từ khóa kích hoạt';
$lang['modal_title'] = 'Chọn nhân viên hỗ trợ';
$lang['close_btn'] = 'Đóng';
$lang['save_btn'] = 'Lưu';
$lang['support_agent'] = 'Nhân viên hỗ trợ';
$lang['change_support_agent'] = 'Thay đổi nhân viên hỗ trợ';
$lang['replay_message'] = 'Bạn không thể gửi tin nhắn sau 24 giờ.';
$lang['support_agent_note'] = '<strong>Chú ý: </strong>Khi bạn kích hoạt tính năng nhân viên hỗ trợ, người phụ trách khách hàng sẽ tự động được phân công cho cuộc trò chuyện. Quản trị viên cũng có thể phân công một nhân viên mới từ trang trò chuyện.';
$lang['permission_bot_clone'] = 'Nhân bản Bot';
$lang['remove_chat'] = 'Xóa cuộc trò chuyện';
$lang['default_message_on_no_match'] = 'Tin nhắn mặc định - nếu không có từ khóa nào khớp';
$lang['default_message_note'] = '<strong>Chú ý: </strong>Bật tùy chọn này sẽ làm tăng tải webhook của bạn. Để biết thêm thông tin, vui lòng truy cập <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">liên kết</a>.';
$lang['whatsbot_connect_account'] = 'Kết nối tài khoản Whatsbot';
$lang['whatsbot_message_bot'] = 'Bot nhắn tin Whatsbot';
$lang['whatsbot_template_bot'] = 'Bot mẫu Whatsbot';
$lang['whatsbot_template'] = 'Mẫu Whatsbot';
$lang['whatsbot_campaigns'] = 'Chiến dịch Whatsbot';
$lang['whatsbot_chat'] = 'Trò chuyện Whatsbot';
$lang['whatsbot_log_activity'] = 'Nhật ký hoạt động Whatsbot';
$lang['message_templates_not_exists_note'] = 'Thiếu quyền truy cập mẫu Meta. Vui lòng bật nó trong tài khoản Meta của bạn';

// v1.2.0
$lang['ai_prompt'] = 'Gợi ý AI';
$lang['ai_prompt_note'] = 'Đối với gợi ý AI, vui lòng nhập tin nhắn để kích hoạt tính năng, hoặc sử dụng gợi ý AI nếu đã được kích hoạt';
$lang['emojis'] = 'Biểu tượng cảm xúc';
$lang['translate'] = 'Dịch';
$lang['change_tone'] = 'Thay đổi giọng điệu';
$lang['professional'] = 'Chuyên nghiệp';
$lang['friendly'] = 'Thân thiện';
$lang['empathetic'] = 'Thấu cảm';
$lang['straightforward'] = 'Rõ ràng';
$lang['simplify_language'] = 'Đơn giản hóa ngôn ngữ';
$lang['fix_spelling_and_grammar'] = 'Sửa chính tả & Ngữ pháp';

$lang['ai_integration'] = 'Tích hợp AI';
$lang['open_ai_api'] = 'API OpenAI';
$lang['open_ai_secret_key'] = 'Khóa bí mật OpenAI - <a href="https://platform.openai.com/account/api-keys" target="_blank">Nơi bạn có thể tìm thấy khóa bí mật?</a>';
$lang['chat_text_limit'] = 'Giới hạn văn bản trò chuyện';
$lang['chat_text_limit_note'] = 'Để tối ưu hóa chi phí vận hành, hãy xem xét việc giới hạn số từ trong phản hồi trò chuyện của OpenAI';
$lang['chat_model'] = 'Mô hình trò chuyện';
$lang['openai_organizations'] = 'Tổ chức OpenAI';
$lang['template_type'] = 'Loại mẫu';
$lang['update'] = 'Cập nhật';
$lang['open_ai_key_verification_fail'] = 'Xác minh khóa OpenAI đang chờ từ cài đặt, vui lòng kết nối tài khoản OpenAI của bạn';
$lang['enable_wb_openai'] = 'Kích hoạt OpenAI trong trò chuyện';
$lang['webhook_resend_method'] = 'Phương thức gửi lại webhook';
$lang['search_language'] = 'Tìm kiếm ngôn ngữ...';
$lang['document'] = 'Tài liệu';
$lang['select_document'] = 'Chọn tài liệu';
$lang['attchment_deleted_successfully'] = 'Đã xóa tệp đính kèm thành công';
$lang['attach_image_video_docs'] = 'Đính kèm hình ảnh, video, tài liệu';
$lang['choose_file_type'] = 'Chọn loại tệp';
$lang['max_size'] = 'Kích thước tối đa: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Chiến dịch hàng loạt';
$lang['upload_csv'] = 'Tải lên CSV';
$lang['upload'] = 'Tải lên';
$lang['csv_uploaded_successfully'] = 'Tệp CSV đã được tải lên thành công';
$lang['please_select_file'] = 'Vui lòng chọn tệp CSV';
$lang['phonenumber_field_is_required'] = 'Trường số điện thoại là bắt buộc';
$lang['out_of_the'] = 'Trong số';
$lang['records_in_your_csv_file'] = 'bản ghi trong tệp CSV của bạn,';
$lang['valid_the_campaign_can_be_sent'] = 'các bản ghi hợp lệ.<br /> Chiến dịch có thể được gửi thành công đến những';
$lang['users'] = 'người dùng';
$lang['campaigns_from_csv_file'] = 'Chiến dịch từ tệp CSV';
$lang['download_sample'] = 'Tải xuống mẫu';
$lang['csv_rule_1'] = '1. <b>Yêu cầu cột số điện thoại:</b> Tệp CSV của bạn phải bao gồm một cột có tên "Phoneno." Mỗi bản ghi trong cột này nên chứa một số liên lạc hợp lệ, được định dạng đúng với mã quốc gia, bao gồm cả dấu "+" . <br /><br />';
$lang['csv_rule_2'] = '2. <b>Định dạng và mã hóa CSV:</b> Dữ liệu CSV của bạn nên tuân theo định dạng đã chỉ định. Hàng đầu tiên của tệp CSV của bạn phải chứa các tiêu đề cột, như trong bảng mẫu. Đảm bảo rằng tệp của bạn được mã hóa theo định dạng UTF-8 để tránh bất kỳ sự cố mã hóa nào.';
$lang['please_upload_valid_csv_file'] = 'Vui lòng tải lên tệp CSV hợp lệ';
$lang['please_add_valid_number_in_csv_file'] = 'Vui lòng thêm số <b>Phoneno</b> hợp lệ trong tệp CSV';
$lang['total_send_campaign_list'] = 'Tổng số chiến dịch đã gửi: %s';
$lang['sample_data'] = 'Dữ liệu mẫu';
$lang['firstname'] = 'Tên';
$lang['lastname'] = 'Họ';
$lang['phoneno'] = 'Số điện thoại';
$lang['email'] = 'Email';
$lang['country'] = 'Quốc gia';
$lang['download_sample_and_read_rules'] = 'Tải xuống tệp mẫu & Đọc quy tắc';
$lang['please_wait_your_request_in_process'] = 'Vui lòng chờ, yêu cầu của bạn đang được xử lý.';
$lang['whatsbot_bulk_campaign'] = 'Chiến dịch hàng loạt Whatsbot';
$lang['csv_campaign'] = 'Chiến dịch CSV';

// Canned reply
$lang['canned_reply'] = 'Trả lời sẵn';
$lang['canned_reply_menu'] = 'Trả lời sẵn';
$lang['create_canned_reply'] = 'Tạo trả lời sẵn';
$lang['title'] = 'Tiêu đề';
$lang['desc'] = 'Mô tả';
$lang['public'] = 'Công khai';
$lang['action'] = 'Hành động';
$lang['delete_successfully'] = 'Trả lời đã bị xóa.';
$lang['cannot_delete'] = 'Không thể xóa trả lời.';
$lang['whatsbot_canned_reply'] = 'Trả lời sẵn Whatsbot';
$lang['reply'] = 'Trả lời';

//AI Prompts
$lang['ai_prompts'] = 'Gợi ý AI';
$lang['create_ai_prompts'] = 'Tạo gợi ý AI';
$lang['name'] = 'Tên';
$lang['action'] = 'Hành động';
$lang['prompt_name'] = 'Tên gợi ý';
$lang['prompt_action'] = 'Hành động gợi ý';
$lang['whatsbot_ai_prompts'] = 'Gợi ý AI Whatsbot';

// new chat
$lang['replying_to'] = 'Đang trả lời:';
$lang['download_document'] = 'Tải xuống tài liệu';
$lang['custom_prompt'] = 'Gợi ý tùy chỉnh';
$lang['canned_replies'] = 'Trả lời sẵn';
$lang['use_@_to_add_merge_fields'] = 'Sử dụng \'@\' để thêm các trường hợp nhất';
$lang['type_your_message'] = 'Gõ tin nhắn của bạn';
$lang['you_cannot_send_a_message_using_this_number'] = 'Bạn không thể gửi tin nhắn bằng số này.';

// bot flow
$lang['bot_flow'] = 'Luồng bot';
$lang['create_new_flow'] = 'Tạo luồng mới';
$lang['flow_name'] = 'Tên luồng';
$lang['flow'] = 'Luồng';
$lang['bot_flow_builder'] = 'Trình tạo luồng bot';
$lang['you_can_not_upload_file_type'] = 'Bạn không thể tải lên tệp loại <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Luồng bot Whatsbot';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Tự động xóa lịch sử trò chuyện';
$lang['enable_auto_clear_chat_history'] = 'Bật tự động xóa lịch sử trò chuyện';
$lang['auto_clear_time'] = 'Thời gian xóa tự động';
$lang['clear_chat_history_note'] = '<strong>Ghi chú: </strong> Nếu bạn bật tính năng xóa lịch sử trò chuyện tự động, nó sẽ tự động xóa lịch sử trò chuyện dựa trên số ngày bạn chỉ định, mỗi khi cron job chạy.';
$lang['source'] = 'Nguồn';
$lang['groups'] = 'Nhóm';


// v1.3.3
$lang['click_user_to_chat'] = 'Nhấp vào người dùng để trò chuyện';
$lang['searching'] = 'Đang tìm kiếm...';
$lang['filters'] = 'Bộ lọc';
$lang['relation_type'] = 'Loại quan hệ';
$lang['groups'] = 'Nhóm';
$lang['source'] = 'Nguồn';
$lang['status'] = 'Trạng thái';
$lang['select_type'] = 'Chọn loại';
$lang['select_agents'] = 'Chọn đại lý';
$lang['select_group'] = 'Chọn nhóm';
$lang['select_source'] = 'Chọn nguồn';
$lang['select_status'] = 'Chọn trạng thái';
$lang['agents'] = 'Đại lý';

// v1.4.2
$lang['read_only'] = 'Chỉ đọc';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';

$lang['send_location'] = 'Send Location';
$lang['search_for_a_place'] = 'Search for a place';
$lang['address_optional'] = 'Address (optional)';
$lang['latitude'] = 'Latitude';
$lang['longitude'] = 'Longitude';
$lang['drag_the_maker_to_set_the_location'] = 'Drag the marker to set the location or search for a place.';
$lang['use_my_location'] = 'Use My Current Location';
$lang['send_location'] = 'Send Location';

$lang['initiate_chat'] = 'Initiate Chat';
$lang['please_select_at_least_one_lead'] = 'Please select at least one lead';
$lang['chat_initiated_successfully'] = 'Chat Initiated  successfuly';
$lang['something_went_wrong'] = "Something Went Wrong";

$lang['video'] = "Video";
$lang['select_video'] = "Select Video";

$lang['whatsbot_cron'] = 'Whatsbot Cron';

// Session Management
$lang['session_management']                 = 'WhatsApp Session Management';
$lang['enable_session_management']          = 'Enable session management';
$lang['session_management_help_text']       = 'WhatsApp Cloud API has a 24-hour session limit for business-initiated messages. Enable this to send a reminder before the session expires.';
$lang['session_expiry_message']             = 'Session expiry message';
$lang['session_expiry_message_help']        = 'This message will be sent to customers before their 24-hour session expires.';
$lang['session_expiry_hours']               = 'Hours before expiry to send reminder';
$lang['session_expiry_hours_help']          = 'Set how many hours after the last customer message to send the reminder (max 23 hours).';
$lang['include_session_reset_button']       = 'Include quick reply button';
$lang['include_session_reset_button_help']  = 'Add a quick reply button to make it easier for customers to respond.';
$lang['session_management_note']            = 'Note: This feature requires the cron job to be properly configured. The cron job should run at least once per hour.';
$lang['session_reset_button_text']          = 'Continue Conversation';
$lang['whatsapp_session_management']        = 'WhatsApp Session Management';
$lang['active_sessions']                    = 'Active Sessions';
$lang['expiring_sessions']                  = 'Expiring Sessions';
$lang['sessions_reset_today']               = 'Sessions Reset Today';
$lang['sessions_reset_week']                = 'Sessions Reset (7 Days)';
$lang['session_management_disabled']        = 'WhatsApp Session Management is currently disabled.';

// Dashboard and Reports
$lang['session_reset_count']                = 'Session Reset Count';
$lang['session_expired']                    = 'Session Expired';
$lang['session_active']                     = 'Session Active';
$lang['session_reset_success_rate']         = 'Reset Success Rate';
$lang['customer_responded']                 = 'Customer Responded';
$lang['customer_not_responded']             = 'Customer Did Not Respond';
$lang['enable_now']                         = 'Enable Now';
