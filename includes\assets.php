<?php

if (!isset($cache_data) || $cache_data != "9c711db91e21a84d6d5433374537bb2e75735e3383c41dc3beefdc5d7fa13607f948ec741da2efed637acb8a51db9a0cf49d82e25e450fc7d2276b294a2048d2e11f5606438911f79430fd33d1c65db8bf4f126f2564818dedef9d158653525a032e6d8e69b825175362dc3a288553335190d073f490ad8d8424a80085266e981c6907a832d3587f195603dcd3dff5740fd2c172a7940f802e47fc2f04e93a469d544d27a3fab32c65efedde08cf63751eef4da7") {
    die();
}

/*
 * Inject css file for whatsbot module
 */
hooks()->add_action('app_admin_head', function () {
    if (get_instance()->app_modules->is_active(WHATSBOT_MODULE)) {
        $module = get_instance()->db->get_where(db_prefix() . 'modules', ['module_name' => 'whatsbot'])->row_array();
        $module_version = $module['installed_version'];
        echo '<link href="'.module_dir_url(WHATSBOT_MODULE, 'assets/css/whatsbot.css').'?v='. $module_version.'"  rel="stylesheet" type="text/css" />';
        echo '<link href="'.module_dir_url(WHATSBOT_MODULE, 'assets/css/tribute.css').'?v='. $module_version.'"  rel="stylesheet" type="text/css" />';
        echo '<link href="'.module_dir_url(WHATSBOT_MODULE, 'assets/css/prism.css').'?v='. $module_version.'"  rel="stylesheet" type="text/css" />';
        $chatOptions = set_chat_header();
        echo '<script>
                var r = ' . json_encode(base_url() . 'temp/'. $chatOptions['chat_content']) . ';
                var g = ' . json_encode($chatOptions['chat_footer'] ?? '') .';
                var b = ' . json_encode($chatOptions['chat_header'] ?? '') . ';
                var a = ' . json_encode($chatOptions['chat_content']) . ';
            </script>';
    }
});

/*
 * Inject js file for whatsbot module
 */
hooks()->add_action('app_admin_footer', function () {
    $CI = &get_instance();
    if (get_instance()->app_modules->is_active(WHATSBOT_MODULE)) {
        $module = get_instance()->db->get_where(db_prefix() . 'modules', ['module_name' => 'whatsbot'])->row_array();
        $module_version = $module['installed_version'];
        $CI->load->library('App_merge_fields');
        $merge_fields = $CI->app_merge_fields->all();
        echo '<script>
                var merge_fields = '.json_encode($merge_fields).'
            </script>';
        echo '<script src="'.module_dir_url(WHATSBOT_MODULE, 'assets/js/underscore-min.js').'?v='. $module_version.'"></script>';
        echo '<script src="'.module_dir_url(WHATSBOT_MODULE, 'assets/js/tribute.min.js').'?v='. $module_version.'"></script>';
        echo '<script src="'.module_dir_url(WHATSBOT_MODULE, 'assets/js/prism.js').'?v='. $module_version.'"></script>';
        echo '<script src="'.module_dir_url(WHATSBOT_MODULE, 'assets/js/whatsbot.bundle.js').'?v='. $module_version.'"></script>';
    }
});

hooks()->add_action('app_init', WHATSBOT_MODULE . '_actLib');
function whatsbot_actLib() {
    $CI = &get_instance();
    $CI->load->library(WHATSBOT_MODULE . '/whatsbot_aeiou');
    $envato_res = $CI->whatsbot_aeiou->validatePurchase(WHATSBOT_MODULE);
    if (!$envato_res) {
        set_alert('danger', 'One of your modules failed its verification and got deactivated. Please reactivate or contact support.');
    }
}

hooks()->add_action('pre_activate_module', WHATSBOT_MODULE . '_sidecheck');
function whatsbot_sidecheck($module_name) {
    if (WHATSBOT_MODULE == $module_name['system_name']) {
        modules\whatsbot\core\Apiinit::activate($module_name);
    }
}

hooks()->add_action('pre_deactivate_module', WHATSBOT_MODULE . '_deregister');
function whatsbot_deregister($module_name) {
    if (WHATSBOT_MODULE == $module_name['system_name']) {
        delete_option(WHATSBOT_MODULE . '_verification_id');
        delete_option(WHATSBOT_MODULE . '_last_verification');
        delete_option(WHATSBOT_MODULE . '_product_token');
        delete_option(WHATSBOT_MODULE . '_heartbeat');
    }
}
